<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>XDown Extension Options</title>
    <style>
        body {
            width: 180px;
            background-color: #f8f7f9;
            margin: 0;
            margin-top: 4px;
            font-size: 11px;
        }
        input {
            top: -1px;
        }
        label {
            margin-left: 2px;
        }
        img {
            width: 32px;
            display: inline;
        }
        .xdown_title {
            text-align: center;
        }
        .xdown_title_txt {
            font-size: 16px;
            position: relative;
            top: -7px;
        }

        .menu-entry {
            margin-top: 4px;
            margin-bottom: 4px;
            padding: 3px 4px 3px 8px;
            cursor: pointer;
        }
        .menu-entry:hover {
            -webkit-transition: all 0.3s ease-out;
            background: #F2F2F2;
        }
        .menu-entry .txt {
            margin-left: 20px;
        }
        .menu-info {
            padding: 3px 4px 3px 8px;
        }
        .menu-info .txt {
            margin-left: 20px;
            display: block;
        }
        .menu-entry {position: relative;}
        .menu-entry.checked .txt:before {
            content: '';
            background-image: url(images/checked.png);
            background-size: contain;
            width: 14px;
            height: 12px;
            display: block;
            position: absolute;
            top: 50%;
            margin-top: -6px;
        }

        .bottom {
            margin-bottom: 6px;
        }
        
        hr { border: 0; height: 0; border-top: 1px solid rgba(0, 0, 0, 0.2); }
    </style>

    <script src="js/web_extension.js"></script>
    <script src="js/xdown_utils.js"></script>
</head>
<body>

<div>
    <div class="xdown_title">
        <img src="images/xdown_24.png" />
        <span class="xdown_title_txt">XDown Manager</span>
    </div>
    <hr/>
    <div class="xdown_content">
        <div class="bottom">
            <div class="menu-entry" id="settingXDownLink">
                <span class="txt" id="spanSettingText">设置 XDown</span>
            </div>
            <div class="menu-entry" id="aboutXDownLink">
                <span class="txt" id="spanAoubtText">关于 XDown</span>
            </div>
        </div>
    </div>
</div>
<script src="xdown_settings.js"></script>
</body>
</html>