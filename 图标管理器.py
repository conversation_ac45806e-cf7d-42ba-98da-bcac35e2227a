#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 图标管理器
负责图标下载、格式转换、缓存管理和图标设置

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import os
import sys
import requests
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import json
import hashlib
import threading
from urllib.parse import urlparse
import time

# 图像处理
try:
    from PIL import Image, ImageDraw
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("⚠️ Pillow未安装，图标处理功能将受限")

# 导入配置管理器
try:
    from 配置管理器 import config_manager, get_config
    from 快捷方式管理器 import shortcut_manager
except ImportError:
    print("❌ 无法导入配置管理器，请确保配置管理器.py文件存在")
    sys.exit(1)

class IconManager:
    """图标管理器 - 图标下载、处理和管理"""
    
    def __init__(self):
        """初始化图标管理器"""
        self.config = config_manager
        self.project_root = self.config.get_project_root()
        self.icons_dir = self.config.get_icons_dir()
        self.cache_dir = self.icons_dir / "缓存"
        
        # 确保目录存在
        self.icons_dir.mkdir(exist_ok=True)
        self.cache_dir.mkdir(exist_ok=True)
        
        # 图标缓存
        self.icon_cache = {}
        self.download_queue = []
        self.download_threads = []
        
        self._setup_logging()
        self._load_icon_cache()
        
        self.logger.info("图标管理器初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.project_root / "日志"
        log_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger("IconManager")
        if not self.logger.handlers:
            handler = logging.FileHandler(log_dir / "图标管理器.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _load_icon_cache(self):
        """加载图标缓存信息"""
        try:
            cache_file = self.cache_dir / "icon_cache.json"
            if cache_file.exists():
                with open(cache_file, 'r', encoding='utf-8') as f:
                    self.icon_cache = json.load(f)
                self.logger.info(f"图标缓存加载成功: {len(self.icon_cache)}个条目")
            else:
                self.icon_cache = {}
                self.logger.info("创建新的图标缓存")
        except Exception as e:
            self.logger.error(f"加载图标缓存失败: {e}")
            self.icon_cache = {}
    
    def _save_icon_cache(self):
        """保存图标缓存信息"""
        try:
            cache_file = self.cache_dir / "icon_cache.json"
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.icon_cache, f, ensure_ascii=False, indent=2)
            self.logger.info("图标缓存保存成功")
        except Exception as e:
            self.logger.error(f"保存图标缓存失败: {e}")
    
    def get_available_icons(self) -> List[Dict[str, str]]:
        """获取可用图标列表"""
        try:
            icons = []
            
            # 扫描图标目录
            for icon_file in self.icons_dir.glob("*.png"):
                if icon_file.is_file():
                    icon_info = {
                        "name": icon_file.stem,
                        "path": str(icon_file),
                        "size": icon_file.stat().st_size,
                        "type": "local"
                    }
                    icons.append(icon_info)
            
            # 添加ICO文件
            for icon_file in self.icons_dir.glob("*.ico"):
                if icon_file.is_file():
                    icon_info = {
                        "name": icon_file.stem,
                        "path": str(icon_file),
                        "size": icon_file.stat().st_size,
                        "type": "local"
                    }
                    icons.append(icon_info)
            
            self.logger.info(f"找到 {len(icons)} 个本地图标")
            return icons
            
        except Exception as e:
            self.logger.error(f"获取可用图标失败: {e}")
            return []
    
    def download_icon_from_url(self, url: str, name: str, callback=None) -> Optional[str]:
        """
        从URL下载图标
        
        Args:
            url: 图标URL
            name: 图标名称
            callback: 下载完成回调函数
            
        Returns:
            下载的图标文件路径
        """
        try:
            # 检查缓存
            url_hash = hashlib.md5(url.encode()).hexdigest()
            cache_key = f"{name}_{url_hash}"
            
            if cache_key in self.icon_cache:
                cached_path = Path(self.icon_cache[cache_key])
                if cached_path.exists():
                    self.logger.info(f"使用缓存图标: {name}")
                    if callback:
                        callback(True, str(cached_path), "使用缓存")
                    return str(cached_path)
            
            # 下载图标
            self.logger.info(f"开始下载图标: {name} from {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            timeout = get_config('icon_settings.download_timeout', 30)
            response = requests.get(url, headers=headers, timeout=timeout, stream=True)
            response.raise_for_status()
            
            # 确定文件扩展名
            content_type = response.headers.get('content-type', '')
            if 'image/png' in content_type:
                ext = '.png'
            elif 'image/jpeg' in content_type or 'image/jpg' in content_type:
                ext = '.jpg'
            elif 'image/x-icon' in content_type or 'image/vnd.microsoft.icon' in content_type:
                ext = '.ico'
            else:
                # 从URL推断
                parsed_url = urlparse(url)
                path_ext = Path(parsed_url.path).suffix.lower()
                ext = path_ext if path_ext in ['.png', '.jpg', '.jpeg', '.ico'] else '.png'
            
            # 保存文件
            icon_path = self.cache_dir / f"{name}_{url_hash}{ext}"
            
            with open(icon_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 验证图像
            if self._validate_image(icon_path):
                # 转换为PNG格式
                png_path = self._convert_to_png(icon_path, name)
                if png_path:
                    # 更新缓存
                    self.icon_cache[cache_key] = str(png_path)
                    self._save_icon_cache()
                    
                    self.logger.info(f"图标下载成功: {name}")
                    if callback:
                        callback(True, str(png_path), "下载成功")
                    return str(png_path)
            
            # 下载失败，删除文件
            if icon_path.exists():
                icon_path.unlink()
            
            self.logger.error(f"图标下载失败: {name} - 无效图像")
            if callback:
                callback(False, "", "无效图像")
            return None
            
        except Exception as e:
            self.logger.error(f"下载图标失败: {name} - {e}")
            if callback:
                callback(False, "", str(e))
            return None
    
    def _validate_image(self, image_path: Path) -> bool:
        """验证图像文件"""
        try:
            if not PIL_AVAILABLE:
                # 简单的文件大小检查
                return image_path.stat().st_size > 100
            
            with Image.open(image_path) as img:
                img.verify()
            return True
        except Exception:
            return False
    
    def _convert_to_png(self, source_path: Path, name: str) -> Optional[Path]:
        """转换图像为PNG格式"""
        try:
            if not PIL_AVAILABLE:
                # 如果没有PIL，直接复制文件
                if source_path.suffix.lower() == '.png':
                    target_path = self.icons_dir / f"{name}.png"
                    import shutil
                    shutil.copy2(source_path, target_path)
                    return target_path
                return source_path
            
            target_path = self.icons_dir / f"{name}.png"
            
            with Image.open(source_path) as img:
                # 转换为RGBA模式
                if img.mode != 'RGBA':
                    img = img.convert('RGBA')
                
                # 调整大小
                target_size = get_config('icon_settings.default_icon_size', 64)
                img = img.resize((target_size, target_size), Image.Resampling.LANCZOS)
                
                # 保存为PNG
                img.save(target_path, 'PNG', optimize=True)
            
            self.logger.info(f"图标转换成功: {name}")
            return target_path
            
        except Exception as e:
            self.logger.error(f"图标转换失败: {name} - {e}")
            return None
    
    def download_icon_async(self, url: str, name: str, callback=None):
        """异步下载图标"""
        def download_worker():
            self.download_icon_from_url(url, name, callback)
        
        thread = threading.Thread(target=download_worker, daemon=True)
        thread.start()
        self.download_threads.append(thread)
    
    def create_default_icon(self, name: str, color: str = "#2196F3") -> Optional[str]:
        """
        创建默认图标
        
        Args:
            name: 图标名称
            color: 图标颜色
            
        Returns:
            创建的图标文件路径
        """
        try:
            if not PIL_AVAILABLE:
                self.logger.warning("PIL不可用，无法创建默认图标")
                return None
            
            size = get_config('icon_settings.default_icon_size', 64)
            icon_path = self.icons_dir / f"{name}.png"
            
            # 创建图标
            img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # 绘制圆形背景
            margin = size // 8
            draw.ellipse([margin, margin, size-margin, size-margin], fill=color)
            
            # 绘制文字
            if len(name) > 0:
                # 简单的文字绘制（第一个字符）
                text = name[0].upper()
                font_size = size // 2
                
                # 计算文字位置（居中）
                text_bbox = draw.textbbox((0, 0), text)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
                x = (size - text_width) // 2
                y = (size - text_height) // 2
                
                draw.text((x, y), text, fill='white')
            
            # 保存图标
            img.save(icon_path, 'PNG', optimize=True)
            
            self.logger.info(f"默认图标创建成功: {name}")
            return str(icon_path)
            
        except Exception as e:
            self.logger.error(f"创建默认图标失败: {name} - {e}")
            return None
    
    def set_browser_icon(self, browser_name: str, icon_path: str) -> bool:
        """
        为浏览器设置图标
        
        Args:
            browser_name: 浏览器名称
            icon_path: 图标文件路径
            
        Returns:
            是否设置成功
        """
        try:
            browsers_dir = self.config.get_browsers_dir()
            browser_dir = browsers_dir / browser_name
            
            if not browser_dir.exists():
                self.logger.error(f"浏览器目录不存在: {browser_name}")
                return False
            
            # 复制图标到浏览器目录
            source_path = Path(icon_path)
            if not source_path.exists():
                self.logger.error(f"图标文件不存在: {icon_path}")
                return False
            
            target_path = browser_dir / f"{browser_name}.png"
            
            import shutil
            shutil.copy2(source_path, target_path)
            
            # 更新快捷方式图标
            if shortcut_manager.update_shortcut_icon(browser_name, str(target_path)):
                self.logger.info(f"浏览器图标设置成功: {browser_name}")
                return True
            else:
                self.logger.warning(f"快捷方式图标更新失败: {browser_name}")
                return True  # 图标复制成功就算成功
                
        except Exception as e:
            self.logger.error(f"设置浏览器图标失败: {browser_name} - {e}")
            return False
    
    def get_browser_icon(self, browser_name: str) -> Optional[str]:
        """获取浏览器当前图标路径"""
        try:
            browsers_dir = self.config.get_browsers_dir()
            browser_dir = browsers_dir / browser_name
            
            if not browser_dir.exists():
                return None
            
            # 查找图标文件
            for ext in ['.png', '.ico', '.jpg', '.jpeg']:
                icon_path = browser_dir / f"{browser_name}{ext}"
                if icon_path.exists():
                    return str(icon_path)
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取浏览器图标失败: {browser_name} - {e}")
            return None
    
    def cleanup_cache(self, max_age_days: int = 30):
        """清理过期缓存"""
        try:
            current_time = time.time()
            max_age_seconds = max_age_days * 24 * 3600
            
            cleaned_count = 0
            
            for cache_file in self.cache_dir.glob("*"):
                if cache_file.is_file():
                    file_age = current_time - cache_file.stat().st_mtime
                    if file_age > max_age_seconds:
                        cache_file.unlink()
                        cleaned_count += 1
            
            # 清理缓存记录
            valid_cache = {}
            for key, path in self.icon_cache.items():
                if Path(path).exists():
                    valid_cache[key] = path
            
            removed_count = len(self.icon_cache) - len(valid_cache)
            self.icon_cache = valid_cache
            self._save_icon_cache()
            
            self.logger.info(f"缓存清理完成: 删除{cleaned_count}个文件, {removed_count}个记录")
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")

# 全局图标管理器实例
icon_manager = IconManager()

def get_available_icons() -> List[Dict[str, str]]:
    """快捷函数：获取可用图标列表"""
    return icon_manager.get_available_icons()

def download_icon(url: str, name: str, callback=None) -> Optional[str]:
    """快捷函数：下载图标"""
    return icon_manager.download_icon_from_url(url, name, callback)

def set_browser_icon(browser_name: str, icon_path: str) -> bool:
    """快捷函数：设置浏览器图标"""
    return icon_manager.set_browser_icon(browser_name, icon_path)

if __name__ == "__main__":
    # 测试图标管理器
    print("🎨 图标管理器测试")
    print(f"PIL支持: {'✅' if PIL_AVAILABLE else '❌'}")
    print(f"图标目录: {icon_manager.icons_dir}")
    
    # 获取可用图标
    icons = get_available_icons()
    print(f"可用图标: {len(icons)}个")
    for icon in icons[:3]:  # 只显示前3个
        print(f"  - {icon['name']} ({icon['type']})")
    
    print("✅ 图标管理器测试完成")
