#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快捷方式管理器测试程序
测试快捷方式创建、删除、图标更新等功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import sys
from pathlib import Path

# 导入快捷方式管理器
try:
    from 快捷方式管理器 import shortcut_manager, create_desktop_shortcut, create_local_shortcut
    from 浏览器管理器 import BrowserManager
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_shortcut_creation():
    """测试快捷方式创建功能"""
    print("🔗 测试快捷方式创建功能")
    
    # 获取现有浏览器列表
    browser_manager = BrowserManager()
    browsers = browser_manager.list_browsers()
    
    if not browsers:
        print("❌ 没有找到浏览器实例，请先创建浏览器")
        return False
    
    print(f"📋 找到 {len(browsers)} 个浏览器实例:")
    for i, browser in enumerate(browsers, 1):
        print(f"  {i}. {browser['name']} ({browser['status']})")
    
    # 选择第一个浏览器进行测试
    test_browser = browsers[0]['name']
    print(f"\n🎯 使用 '{test_browser}' 进行快捷方式测试")
    
    # 测试创建桌面快捷方式
    print("\n1️⃣ 测试创建桌面快捷方式...")
    if create_desktop_shortcut(test_browser):
        print("✅ 桌面快捷方式创建成功")
    else:
        print("❌ 桌面快捷方式创建失败")
    
    # 测试创建本地快捷方式
    print("\n2️⃣ 测试创建本地快捷方式...")
    if create_local_shortcut(test_browser):
        print("✅ 本地快捷方式创建成功")
    else:
        print("❌ 本地快捷方式创建失败")
    
    # 检查快捷方式文件是否存在
    print("\n3️⃣ 验证快捷方式文件...")
    
    # 检查桌面快捷方式
    desktop_path = shortcut_manager._get_desktop_path()
    if desktop_path:
        desktop_shortcut = desktop_path / f"{test_browser}.lnk"
        if desktop_shortcut.exists():
            print(f"✅ 桌面快捷方式存在: {desktop_shortcut}")
        else:
            print(f"❌ 桌面快捷方式不存在: {desktop_shortcut}")
    
    # 检查本地快捷方式
    browser_dir = shortcut_manager.browsers_dir / test_browser
    local_shortcut = browser_dir / f"{test_browser}.lnk"
    if local_shortcut.exists():
        print(f"✅ 本地快捷方式存在: {local_shortcut}")
    else:
        print(f"❌ 本地快捷方式不存在: {local_shortcut}")
    
    return True

def test_icon_update():
    """测试图标更新功能"""
    print("\n🎨 测试图标更新功能")
    
    # 获取现有浏览器列表
    browser_manager = BrowserManager()
    browsers = browser_manager.list_browsers()
    
    if not browsers:
        print("❌ 没有找到浏览器实例")
        return False
    
    test_browser = browsers[0]['name']
    
    # 查找可用图标
    icons_dir = shortcut_manager.config.get_icons_dir()
    available_icons = []
    
    for icon_file in icons_dir.glob("*.png"):
        available_icons.append(str(icon_file))
    
    if not available_icons:
        print("❌ 没有找到可用图标")
        return False
    
    print(f"📋 找到 {len(available_icons)} 个可用图标:")
    for i, icon in enumerate(available_icons[:3], 1):  # 只显示前3个
        icon_name = Path(icon).stem
        print(f"  {i}. {icon_name}")
    
    # 使用第一个图标测试更新
    test_icon = available_icons[0]
    icon_name = Path(test_icon).stem
    
    print(f"\n🎯 使用 '{icon_name}' 图标更新 '{test_browser}' 快捷方式")
    
    if shortcut_manager.update_shortcut_icon(test_browser, test_icon):
        print("✅ 快捷方式图标更新成功")
    else:
        print("❌ 快捷方式图标更新失败")
    
    return True

def test_shortcut_deletion():
    """测试快捷方式删除功能"""
    print("\n🗑️ 测试快捷方式删除功能")
    
    # 获取现有浏览器列表
    browser_manager = BrowserManager()
    browsers = browser_manager.list_browsers()
    
    if not browsers:
        print("❌ 没有找到浏览器实例")
        return False
    
    test_browser = browsers[0]['name']
    
    print(f"🎯 删除 '{test_browser}' 的桌面快捷方式")
    
    if shortcut_manager.delete_desktop_shortcut(test_browser):
        print("✅ 桌面快捷方式删除成功")
    else:
        print("❌ 桌面快捷方式删除失败")
    
    # 验证删除结果
    desktop_path = shortcut_manager._get_desktop_path()
    if desktop_path:
        desktop_shortcut = desktop_path / f"{test_browser}.lnk"
        if not desktop_shortcut.exists():
            print("✅ 桌面快捷方式已被删除")
        else:
            print("❌ 桌面快捷方式仍然存在")
    
    return True

def main():
    """主函数"""
    print("🔗 快捷方式管理器功能测试")
    print("=" * 50)
    
    try:
        # 测试快捷方式创建
        if not test_shortcut_creation():
            print("❌ 快捷方式创建测试失败")
            return
        
        print("\n" + "=" * 50)
        
        # 测试图标更新
        if not test_icon_update():
            print("❌ 图标更新测试失败")
            return
        
        print("\n" + "=" * 50)
        
        # 测试快捷方式删除
        if not test_shortcut_deletion():
            print("❌ 快捷方式删除测试失败")
            return
        
        print("\n" + "=" * 50)
        print("🎉 所有快捷方式功能测试通过！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
