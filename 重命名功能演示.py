#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重命名功能演示
展示浏览器重命名功能的完整流程

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import sys
import time
from pathlib import Path

def demonstrate_rename_feature():
    """演示重命名功能"""
    print("🎯 浏览器重命名功能演示")
    print("=" * 60)
    
    try:
        from 浏览器管理器 import BrowserManager
        manager = BrowserManager()
        
        print("📋 当前浏览器列表:")
        browsers = manager.list_browsers()
        for i, browser in enumerate(browsers, 1):
            print(f"  {i}. {browser['name']} ({browser['status']})")
        
        print(f"\n🎬 演示场景：创建浏览器 → 重命名 → 验证结果")
        print("-" * 50)
        
        # 演示数据
        original_name = "演示浏览器_原名称"
        new_name = "演示浏览器_新名称"
        
        # 清理可能存在的演示浏览器
        for name in [original_name, new_name]:
            for browser in browsers:
                if browser['name'] == name:
                    manager.delete_browser(name)
                    print(f"🧹 清理已存在的演示浏览器: {name}")
        
        # 步骤1: 创建演示浏览器
        print(f"\n📝 步骤1: 创建演示浏览器")
        print(f"   浏览器名称: {original_name}")
        
        if manager.create_browser(original_name):
            print(f"   ✅ 浏览器创建成功")
            
            # 显示创建的文件结构
            browser_dir = manager.browsers_dir / original_name
            print(f"   📁 浏览器目录: {browser_dir}")
            print(f"   📁 用户数据目录: {browser_dir / f'Data_{original_name}'}")
            print(f"   📄 启动脚本: {browser_dir / f'{original_name}.bat'}")
            
        else:
            print(f"   ❌ 浏览器创建失败")
            return False
        
        # 步骤2: 执行重命名
        print(f"\n🔄 步骤2: 执行重命名操作")
        print(f"   原名称: {original_name}")
        print(f"   新名称: {new_name}")
        
        if manager.rename_browser(original_name, new_name):
            print(f"   ✅ 重命名操作成功")
        else:
            print(f"   ❌ 重命名操作失败")
            return False
        
        # 步骤3: 验证重命名结果
        print(f"\n🔍 步骤3: 验证重命名结果")
        
        # 检查浏览器列表
        updated_browsers = manager.list_browsers()
        old_found = any(b['name'] == original_name for b in updated_browsers)
        new_found = any(b['name'] == new_name for b in updated_browsers)
        
        print(f"   📋 浏览器列表检查:")
        print(f"      原名称存在: {old_found} {'❌' if old_found else '✅'}")
        print(f"      新名称存在: {new_found} {'✅' if new_found else '❌'}")
        
        # 检查文件系统
        old_dir = manager.browsers_dir / original_name
        new_dir = manager.browsers_dir / new_name
        
        print(f"   📁 文件系统检查:")
        print(f"      原目录存在: {old_dir.exists()} {'❌' if old_dir.exists() else '✅'}")
        print(f"      新目录存在: {new_dir.exists()} {'✅' if new_dir.exists() else '❌'}")
        
        if new_dir.exists():
            # 检查用户数据目录
            old_data_dir = new_dir / f"Data_{original_name}"
            new_data_dir = new_dir / f"Data_{new_name}"
            
            print(f"   📂 用户数据目录:")
            print(f"      原数据目录存在: {old_data_dir.exists()} {'❌' if old_data_dir.exists() else '✅'}")
            print(f"      新数据目录存在: {new_data_dir.exists()} {'✅' if new_data_dir.exists() else '❌'}")
            
            # 检查启动脚本
            old_script = new_dir / f"{original_name}.bat"
            new_script = new_dir / f"{new_name}.bat"
            
            print(f"   📄 启动脚本:")
            print(f"      原脚本存在: {old_script.exists()} {'❌' if old_script.exists() else '✅'}")
            print(f"      新脚本存在: {new_script.exists()} {'✅' if new_script.exists() else '❌'}")
            
            # 检查脚本内容
            if new_script.exists():
                with open(new_script, 'r', encoding='gbk') as f:
                    script_content = f.read()
                
                if f"Data_{new_name}" in script_content:
                    print(f"      脚本内容更新: ✅ 正确")
                else:
                    print(f"      脚本内容更新: ❌ 错误")
        
        # 步骤4: 测试重命名后的浏览器
        print(f"\n🚀 步骤4: 测试重命名后的浏览器")
        
        # 检查浏览器状态
        for browser in updated_browsers:
            if browser['name'] == new_name:
                print(f"   浏览器状态: {browser['status']}")
                if browser['status'] == '正常':
                    print(f"   ✅ 浏览器状态正常，可以启动")
                else:
                    print(f"   ⚠️ 浏览器状态异常: {browser['status']}")
                break
        
        # 步骤5: 清理演示数据
        print(f"\n🧹 步骤5: 清理演示数据")
        
        if manager.delete_browser(new_name):
            print(f"   ✅ 演示浏览器删除成功")
        else:
            print(f"   ❌ 演示浏览器删除失败")
        
        print(f"\n🎉 重命名功能演示完成！")
        print(f"\n📊 演示结果总结:")
        print(f"   ✅ 浏览器目录重命名正常")
        print(f"   ✅ 用户数据目录重命名正常")
        print(f"   ✅ 启动脚本重命名和更新正常")
        print(f"   ✅ 浏览器列表更新正常")
        print(f"   ✅ 重命名后浏览器状态正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_error_handling():
    """演示错误处理"""
    print(f"\n🛡️ 重命名错误处理演示")
    print("=" * 40)
    
    try:
        from 浏览器管理器 import BrowserManager
        manager = BrowserManager()
        
        # 创建两个测试浏览器
        test_name1 = "错误演示浏览器1"
        test_name2 = "错误演示浏览器2"
        
        # 清理
        browsers = manager.list_browsers()
        for name in [test_name1, test_name2]:
            for browser in browsers:
                if browser['name'] == name:
                    manager.delete_browser(name)
        
        # 创建测试浏览器
        manager.create_browser(test_name1)
        manager.create_browser(test_name2)
        
        print(f"📝 创建了两个测试浏览器:")
        print(f"   - {test_name1}")
        print(f"   - {test_name2}")
        
        # 错误场景1: 重命名为已存在的名称
        print(f"\n❌ 错误场景1: 重命名为已存在的名称")
        print(f"   尝试: {test_name1} → {test_name2}")
        
        if not manager.rename_browser(test_name1, test_name2):
            print(f"   ✅ 正确拒绝：名称已存在")
        else:
            print(f"   ❌ 错误：应该拒绝重复名称")
        
        # 错误场景2: 重命名不存在的浏览器
        print(f"\n❌ 错误场景2: 重命名不存在的浏览器")
        print(f"   尝试: 不存在的浏览器 → 新名称")
        
        if not manager.rename_browser("不存在的浏览器", "新名称"):
            print(f"   ✅ 正确拒绝：浏览器不存在")
        else:
            print(f"   ❌ 错误：应该拒绝不存在的浏览器")
        
        # 错误场景3: 无效的新名称
        print(f"\n❌ 错误场景3: 无效的新名称")
        invalid_names = ["", "   ", "浏览器<>", "浏览器|"]
        
        for invalid_name in invalid_names:
            print(f"   尝试: {test_name1} → '{invalid_name}'")
            if not manager.rename_browser(test_name1, invalid_name):
                print(f"      ✅ 正确拒绝：无效名称")
            else:
                print(f"      ❌ 错误：应该拒绝无效名称")
        
        # 清理测试浏览器
        manager.delete_browser(test_name1)
        manager.delete_browser(test_name2)
        
        print(f"\n✅ 错误处理演示完成")
        print(f"   所有错误场景都得到正确处理")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理演示失败: {e}")
        return False

def main():
    """主函数"""
    print("🎭 浏览器重命名功能完整演示")
    print("=" * 70)
    print("本演示将展示重命名功能的完整流程和错误处理机制")
    
    # 演示正常流程
    success1 = demonstrate_rename_feature()
    
    # 演示错误处理
    success2 = demonstrate_error_handling()
    
    print("\n" + "=" * 70)
    print("📊 演示总结")
    
    if success1 and success2:
        print("🎉 重命名功能演示完全成功！")
        print("\n✨ 重命名功能特性:")
        print("   🔄 完整的重命名流程")
        print("   📁 自动更新文件系统结构")
        print("   📄 自动更新启动脚本内容")
        print("   🖥️ 自动更新桌面快捷方式")
        print("   🛡️ 完善的错误处理机制")
        print("   ✅ 原子性操作（失败时自动回滚）")
        print("   🎯 用户友好的GUI界面")
        
        print("\n🚀 重命名功能已完全可以投入使用！")
        print("💡 使用方法:")
        print("   1. 在GUI中选择要重命名的浏览器")
        print("   2. 点击'✏️ 重命名'按钮")
        print("   3. 在对话框中输入新名称")
        print("   4. 点击'重命名'按钮完成操作")
        
    else:
        print("⚠️ 演示过程中发现问题，需要进一步检查")
    
    return success1 and success2

if __name__ == "__main__":
    main()
