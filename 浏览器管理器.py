#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 浏览器管理器（命令行版本）
核心业务逻辑，浏览器实例的创建、管理、启动

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import os
import sys
import json
import shutil
import subprocess
from pathlib import Path
from typing import List, Dict, Optional
import logging

# 导入配置管理器
try:
    from 配置管理器 import config_manager, get_config
except ImportError:
    print("❌ 无法导入配置管理器，请确保配置管理器.py文件存在")
    sys.exit(1)

class BrowserManager:
    """浏览器管理器 - 核心业务逻辑"""
    
    def __init__(self):
        """初始化浏览器管理器"""
        self.config = config_manager
        self.project_root = self.config.get_project_root()
        self.browsers_dir = self.config.get_browsers_dir()
        self.chrome_path = self.config.get_chrome_path()
        
        # 确保目录存在
        self.browsers_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        
        self.logger.info("浏览器管理器初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.project_root / "日志"
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "浏览器管理器.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger("BrowserManager")
    
    def list_browsers(self) -> List[Dict[str, str]]:
        """列出所有浏览器实例"""
        browsers = []
        
        if not self.browsers_dir.exists():
            return browsers
        
        for item in self.browsers_dir.iterdir():
            if item.is_dir():
                browser_info = {
                    "name": item.name,
                    "path": str(item),
                    "data_dir": str(item / f"Data_{item.name}"),
                    "icon": self._find_browser_icon(item),
                    "status": "正常" if self._check_browser_health(item) else "异常"
                }
                browsers.append(browser_info)
        
        return browsers
    
    def _find_browser_icon(self, browser_dir: Path) -> Optional[str]:
        """查找浏览器图标"""
        icon_extensions = ['.ico', '.png', '.jpg', '.jpeg']
        
        for ext in icon_extensions:
            for icon_file in browser_dir.glob(f"*{ext}"):
                return str(icon_file)
        
        return None
    
    def _check_browser_health(self, browser_dir: Path) -> bool:
        """检查浏览器实例健康状态"""
        required_items = [
            "Chrome-bin",  # Chrome程序目录
            f"Data_{browser_dir.name}",  # 用户数据目录
        ]
        
        for item in required_items:
            if not (browser_dir / item).exists():
                return False
        
        return True
    
    def create_browser(self, name: str, icon_type: str = "generic") -> bool:
        """
        创建新的浏览器实例
        
        Args:
            name: 浏览器名称
            icon_type: 图标类型
            
        Returns:
            是否创建成功
        """
        try:
            # 验证名称
            if not self._validate_browser_name(name):
                return False
            
            browser_dir = self.browsers_dir / name
            if browser_dir.exists():
                self.logger.error(f"浏览器 {name} 已存在")
                return False
            
            # 创建浏览器目录
            browser_dir.mkdir(parents=True)
            self.logger.info(f"创建浏览器目录: {browser_dir}")
            
            # 复制Chrome程序文件
            if not self._setup_chrome_files(browser_dir):
                shutil.rmtree(browser_dir)
                return False
            
            # 创建用户数据目录
            data_dir = browser_dir / f"Data_{name}"
            data_dir.mkdir()
            self.logger.info(f"创建用户数据目录: {data_dir}")
            
            # 设置图标
            self._setup_browser_icon(browser_dir, icon_type)
            
            # 创建启动脚本
            self._create_launch_script(browser_dir, name)
            
            self.logger.info(f"浏览器 {name} 创建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"创建浏览器失败: {e}")
            return False
    
    def _validate_browser_name(self, name: str) -> bool:
        """验证浏览器名称"""
        if not name or not name.strip():
            self.logger.error("浏览器名称不能为空")
            return False
        
        # 检查非法字符
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
        for char in invalid_chars:
            if char in name:
                self.logger.error(f"浏览器名称包含非法字符: {char}")
                return False
        
        return True
    
    def _setup_chrome_files(self, browser_dir: Path) -> bool:
        """设置Chrome程序文件"""
        try:
            chrome_source = self.project_root / "GoogleChromePortable"
            chrome_target = browser_dir / "Chrome-bin"
            
            if not chrome_source.exists():
                self.logger.error("Chrome Portable源文件不存在")
                return False
            
            # 复制Chrome文件
            shutil.copytree(chrome_source, chrome_target)
            self.logger.info(f"Chrome文件复制完成: {chrome_target}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置Chrome文件失败: {e}")
            return False
    
    def _setup_browser_icon(self, browser_dir: Path, icon_type: str):
        """设置浏览器图标"""
        try:
            icons_dir = self.config.get_icons_dir()
            icon_file = icons_dir / f"{icon_type}.png"
            
            if icon_file.exists():
                target_icon = browser_dir / f"{icon_type}.ico"
                shutil.copy2(icon_file, target_icon)
                self.logger.info(f"图标设置完成: {target_icon}")
            else:
                self.logger.warning(f"图标文件不存在: {icon_file}")
                
        except Exception as e:
            self.logger.error(f"设置图标失败: {e}")
    
    def _create_launch_script(self, browser_dir: Path, name: str):
        """创建启动脚本"""
        try:
            script_content = f'''@echo off
cd /d "%~dp0"
start "" "Chrome-bin\\GoogleChromePortable.exe" --user-data-dir="Data_{name}" --disable-web-security --disable-features=VizDisplayCompositor
'''
            
            script_file = browser_dir / f"{name}.bat"
            with open(script_file, 'w', encoding='gbk') as f:
                f.write(script_content)
            
            self.logger.info(f"启动脚本创建完成: {script_file}")
            
        except Exception as e:
            self.logger.error(f"创建启动脚本失败: {e}")
    
    def launch_browser(self, name: str) -> bool:
        """启动浏览器实例"""
        try:
            browser_dir = self.browsers_dir / name
            if not browser_dir.exists():
                self.logger.error(f"浏览器 {name} 不存在")
                return False
            
            script_file = browser_dir / f"{name}.bat"
            if not script_file.exists():
                self.logger.error(f"启动脚本不存在: {script_file}")
                return False
            
            # 启动浏览器
            subprocess.Popen([str(script_file)], cwd=str(browser_dir))
            self.logger.info(f"浏览器 {name} 启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"启动浏览器失败: {e}")
            return False
    
    def delete_browser(self, name: str) -> bool:
        """删除浏览器实例"""
        try:
            browser_dir = self.browsers_dir / name
            if not browser_dir.exists():
                self.logger.error(f"浏览器 {name} 不存在")
                return False
            
            # 删除整个目录
            shutil.rmtree(browser_dir)
            self.logger.info(f"浏览器 {name} 删除成功")
            return True
            
        except Exception as e:
            self.logger.error(f"删除浏览器失败: {e}")
            return False

def show_menu():
    """显示主菜单"""
    menu = """
╔══════════════════════════════════════════════════════════════╗
║                    🖥️  浏览器管理器 v2.2.1                    ║
╠══════════════════════════════════════════════════════════════╣
║  1. 📋 列出所有浏览器                                          ║
║  2. 🆕 创建新浏览器                                            ║
║  3. 🚀 启动浏览器                                              ║
║  4. 🗑️  删除浏览器                                             ║
║  5. 🔄 刷新列表                                                ║
║  0. 👋 退出程序                                                ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(menu)

def main():
    """主函数"""
    print("🖥️  浏览器管理器 v2.2.1 (命令行版本)")
    print("基于 CogniGraph™ 认知图迹系统\n")
    
    manager = BrowserManager()
    
    while True:
        show_menu()
        choice = input("请选择操作 (0-5): ").strip()
        
        if choice == "1":
            # 列出浏览器
            browsers = manager.list_browsers()
            if not browsers:
                print("📭 暂无浏览器实例")
            else:
                print(f"\n📋 找到 {len(browsers)} 个浏览器实例:")
                for i, browser in enumerate(browsers, 1):
                    print(f"  {i}. {browser['name']} ({browser['status']})")
        
        elif choice == "2":
            # 创建浏览器
            name = input("请输入浏览器名称: ").strip()
            if name:
                if manager.create_browser(name):
                    print(f"✅ 浏览器 {name} 创建成功")
                else:
                    print(f"❌ 浏览器 {name} 创建失败")
        
        elif choice == "3":
            # 启动浏览器
            browsers = manager.list_browsers()
            if not browsers:
                print("📭 暂无浏览器实例")
            else:
                print("\n📋 可用的浏览器:")
                for i, browser in enumerate(browsers, 1):
                    print(f"  {i}. {browser['name']}")
                
                try:
                    index = int(input("请选择要启动的浏览器 (序号): ")) - 1
                    if 0 <= index < len(browsers):
                        browser_name = browsers[index]['name']
                        if manager.launch_browser(browser_name):
                            print(f"🚀 浏览器 {browser_name} 启动成功")
                        else:
                            print(f"❌ 浏览器 {browser_name} 启动失败")
                    else:
                        print("❌ 无效的序号")
                except ValueError:
                    print("❌ 请输入有效的数字")
        
        elif choice == "4":
            # 删除浏览器
            browsers = manager.list_browsers()
            if not browsers:
                print("📭 暂无浏览器实例")
            else:
                print("\n📋 可删除的浏览器:")
                for i, browser in enumerate(browsers, 1):
                    print(f"  {i}. {browser['name']}")
                
                try:
                    index = int(input("请选择要删除的浏览器 (序号): ")) - 1
                    if 0 <= index < len(browsers):
                        browser_name = browsers[index]['name']
                        confirm = input(f"确认删除 {browser_name}? (y/N): ").strip().lower()
                        if confirm == 'y':
                            if manager.delete_browser(browser_name):
                                print(f"🗑️  浏览器 {browser_name} 删除成功")
                            else:
                                print(f"❌ 浏览器 {browser_name} 删除失败")
                        else:
                            print("❌ 取消删除")
                    else:
                        print("❌ 无效的序号")
                except ValueError:
                    print("❌ 请输入有效的数字")
        
        elif choice == "5":
            # 刷新列表
            print("🔄 列表已刷新")
        
        elif choice == "0":
            print("👋 感谢使用浏览器管理器！")
            break
        
        else:
            print("❌ 无效的选择，请重新输入")
        
        print()  # 空行分隔

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序发生错误: {e}")
        sys.exit(1)
