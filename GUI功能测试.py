#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI界面功能测试
自动化测试GUI界面的各项功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
import sys
import time
from pathlib import Path

# 导入GUI模块
try:
    from 浏览器管理器GUI import BrowserManagerGUI
    from 浏览器管理器 import BrowserManager
    from 主题管理器 import theme_manager
    from 快捷方式管理器 import shortcut_manager
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

class GUITester:
    """GUI功能测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = []
        self.browser_manager = BrowserManager()
    
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅" if success else "❌"
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append((test_name, success, message))
    
    def test_gui_initialization(self):
        """测试GUI初始化"""
        print("🖥️ 测试GUI初始化")
        
        try:
            # 创建GUI实例但不运行主循环
            app = BrowserManagerGUI()
            
            # 检查基本属性
            if hasattr(app, 'root'):
                self.log_result("GUI根窗口创建", True)
            else:
                self.log_result("GUI根窗口创建", False, "缺少root属性")
            
            if hasattr(app, 'browser_manager'):
                self.log_result("浏览器管理器集成", True)
            else:
                self.log_result("浏览器管理器集成", False, "缺少browser_manager属性")
            
            if hasattr(app, 'themed_widgets'):
                self.log_result("主题组件列表", True, f"包含{len(app.themed_widgets)}个组件")
            else:
                self.log_result("主题组件列表", False, "缺少themed_widgets属性")
            
            # 检查窗口标题
            title = app.root.title()
            if "浏览器多账号绿色版" in title:
                self.log_result("窗口标题设置", True, title)
            else:
                self.log_result("窗口标题设置", False, f"标题不正确: {title}")
            
            # 检查窗口大小
            geometry = app.root.geometry()
            if "900x700" in geometry:
                self.log_result("窗口大小设置", True, geometry)
            else:
                self.log_result("窗口大小设置", False, f"大小不正确: {geometry}")
            
            # 销毁窗口
            app.root.destroy()
            
            return True
            
        except Exception as e:
            self.log_result("GUI初始化", False, str(e))
            return False
    
    def test_browser_list_functionality(self):
        """测试浏览器列表功能"""
        print("\n📋 测试浏览器列表功能")
        
        try:
            # 获取实际浏览器数量
            browsers = self.browser_manager.list_browsers()
            browser_count = len(browsers)
            
            self.log_result("浏览器数据获取", True, f"找到{browser_count}个浏览器")
            
            # 检查浏览器状态
            healthy_count = 0
            for browser in browsers:
                if browser['status'] == '正常':
                    healthy_count += 1
            
            self.log_result("浏览器健康检查", True, f"{healthy_count}/{browser_count}个浏览器正常")
            
            return True
            
        except Exception as e:
            self.log_result("浏览器列表功能", False, str(e))
            return False
    
    def test_theme_functionality(self):
        """测试主题功能"""
        print("\n🎨 测试主题功能")
        
        try:
            # 获取当前主题
            current_theme = theme_manager.get_current_theme()
            self.log_result("当前主题获取", True, current_theme)
            
            # 获取可用主题
            available_themes = theme_manager.get_available_themes()
            self.log_result("可用主题获取", True, f"{len(available_themes)}个主题")
            
            # 测试颜色获取
            primary_color = theme_manager.get_color('primary')
            if primary_color.startswith('#'):
                self.log_result("主题颜色获取", True, primary_color)
            else:
                self.log_result("主题颜色获取", False, f"颜色格式错误: {primary_color}")
            
            return True
            
        except Exception as e:
            self.log_result("主题功能", False, str(e))
            return False
    
    def test_shortcut_functionality(self):
        """测试快捷方式功能"""
        print("\n🔗 测试快捷方式功能")
        
        try:
            # 检查COM组件支持
            from 快捷方式管理器 import COM_AVAILABLE
            self.log_result("COM组件支持", COM_AVAILABLE, "Windows快捷方式支持")
            
            # 检查桌面路径
            desktop_path = shortcut_manager._get_desktop_path()
            if desktop_path and desktop_path.exists():
                self.log_result("桌面路径获取", True, str(desktop_path))
            else:
                self.log_result("桌面路径获取", False, "无法获取桌面路径")
            
            return True
            
        except Exception as e:
            self.log_result("快捷方式功能", False, str(e))
            return False
    
    def test_file_structure(self):
        """测试文件结构完整性"""
        print("\n📁 测试文件结构完整性")
        
        try:
            # 检查核心文件
            core_files = [
                "启动管理器.py",
                "浏览器管理器.py",
                "浏览器管理器GUI.py",
                "配置管理器.py",
                "主题管理器.py",
                "快捷方式管理器.py",
                "项目配置.json",
                "browsers.cognigraph.json",
                "requirements.txt"
            ]
            
            project_root = self.browser_manager.project_root
            missing_files = []
            
            for file_name in core_files:
                file_path = project_root / file_name
                if file_path.exists():
                    self.log_result(f"文件存在: {file_name}", True)
                else:
                    self.log_result(f"文件存在: {file_name}", False)
                    missing_files.append(file_name)
            
            if not missing_files:
                self.log_result("核心文件完整性", True, "所有核心文件存在")
            else:
                self.log_result("核心文件完整性", False, f"缺失{len(missing_files)}个文件")
            
            # 检查目录结构
            required_dirs = [
                "浏览器实例",
                "默认图标",
                "日志",
                "GoogleChromePortable"
            ]
            
            missing_dirs = []
            for dir_name in required_dirs:
                dir_path = project_root / dir_name
                if dir_path.exists():
                    self.log_result(f"目录存在: {dir_name}", True)
                else:
                    self.log_result(f"目录存在: {dir_name}", False)
                    missing_dirs.append(dir_name)
            
            if not missing_dirs:
                self.log_result("目录结构完整性", True, "所有必需目录存在")
            else:
                self.log_result("目录结构完整性", False, f"缺失{len(missing_dirs)}个目录")
            
            return len(missing_files) == 0 and len(missing_dirs) == 0
            
        except Exception as e:
            self.log_result("文件结构测试", False, str(e))
            return False
    
    def test_integration(self):
        """测试模块集成"""
        print("\n🔗 测试模块集成")
        
        try:
            # 测试配置管理器与主题管理器集成
            from 配置管理器 import get_config
            from 主题管理器 import switch_theme
            
            original_theme = get_config('system_settings.theme')
            self.log_result("配置-主题集成", True, f"当前主题: {original_theme}")
            
            # 测试浏览器管理器与快捷方式管理器集成
            browsers = self.browser_manager.list_browsers()
            if browsers:
                test_browser = browsers[0]['name']
                # 不实际创建快捷方式，只测试接口
                self.log_result("浏览器-快捷方式集成", True, f"可为'{test_browser}'创建快捷方式")
            
            return True
            
        except Exception as e:
            self.log_result("模块集成", False, str(e))
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 GUI功能全面测试")
        print("=" * 60)
        
        tests = [
            self.test_gui_initialization,
            self.test_browser_list_functionality,
            self.test_theme_functionality,
            self.test_shortcut_functionality,
            self.test_file_structure,
            self.test_integration
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"📊 测试总结: {passed}/{total} 个测试通过")
        
        # 详细结果统计
        success_count = sum(1 for _, success, _ in self.test_results if success)
        total_count = len(self.test_results)
        
        print(f"📋 详细结果: {success_count}/{total_count} 个检查项通过")
        
        if passed == total and success_count == total_count:
            print("🎉 所有GUI功能测试通过！")
            return True
        else:
            print("⚠️ 部分测试未通过，请检查上述结果")
            return False

def main():
    """主函数"""
    try:
        tester = GUITester()
        success = tester.run_all_tests()
        
        if success:
            print("\n✅ GUI系统完全正常，可以投入使用！")
        else:
            print("\n❌ GUI系统存在问题，需要修复")
            
    except Exception as e:
        print(f"❌ 测试程序发生错误: {e}")

if __name__ == "__main__":
    main()
