#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重命名功能测试
测试浏览器重命名功能的各种场景

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import sys
import time
from pathlib import Path

def test_backend_rename():
    """测试后端重命名功能"""
    print("🔧 测试后端重命名功能")
    print("-" * 40)
    
    try:
        from 浏览器管理器 import BrowserManager
        manager = BrowserManager()
        
        # 创建测试浏览器
        test_name = "重命名测试浏览器"
        new_name = "重命名后的浏览器"
        
        print(f"1. 创建测试浏览器: {test_name}")
        
        # 先清理可能存在的测试浏览器
        existing_browsers = manager.list_browsers()
        for browser in existing_browsers:
            if browser['name'] in [test_name, new_name]:
                manager.delete_browser(browser['name'])
                print(f"   清理已存在的浏览器: {browser['name']}")
        
        # 创建测试浏览器
        if manager.create_browser(test_name):
            print(f"   ✅ 测试浏览器创建成功")
            
            # 验证浏览器存在
            browsers = manager.list_browsers()
            browser_found = any(b['name'] == test_name for b in browsers)
            
            if browser_found:
                print(f"   ✅ 浏览器在列表中找到")
                
                # 测试重命名
                print(f"\n2. 重命名浏览器: {test_name} → {new_name}")
                
                if manager.rename_browser(test_name, new_name):
                    print(f"   ✅ 重命名成功")
                    
                    # 验证重命名结果
                    updated_browsers = manager.list_browsers()
                    old_found = any(b['name'] == test_name for b in updated_browsers)
                    new_found = any(b['name'] == new_name for b in updated_browsers)
                    
                    if not old_found and new_found:
                        print(f"   ✅ 重命名验证成功")
                        
                        # 检查文件结构
                        old_dir = manager.browsers_dir / test_name
                        new_dir = manager.browsers_dir / new_name
                        
                        if not old_dir.exists() and new_dir.exists():
                            print(f"   ✅ 目录重命名成功")
                            
                            # 检查用户数据目录
                            old_data_dir = new_dir / f"Data_{test_name}"
                            new_data_dir = new_dir / f"Data_{new_name}"
                            
                            if not old_data_dir.exists() and new_data_dir.exists():
                                print(f"   ✅ 用户数据目录重命名成功")
                            else:
                                print(f"   ❌ 用户数据目录重命名失败")
                            
                            # 检查启动脚本
                            old_script = new_dir / f"{test_name}.bat"
                            new_script = new_dir / f"{new_name}.bat"
                            
                            if not old_script.exists() and new_script.exists():
                                print(f"   ✅ 启动脚本重命名成功")
                            else:
                                print(f"   ❌ 启动脚本重命名失败")
                                
                        else:
                            print(f"   ❌ 目录重命名失败")
                    else:
                        print(f"   ❌ 重命名验证失败")
                        print(f"      旧名称存在: {old_found}")
                        print(f"      新名称存在: {new_found}")
                else:
                    print(f"   ❌ 重命名失败")
                    return False
                
                # 清理测试浏览器
                print(f"\n3. 清理测试浏览器")
                if manager.delete_browser(new_name):
                    print(f"   ✅ 测试浏览器清理成功")
                else:
                    print(f"   ❌ 测试浏览器清理失败")
                    
            else:
                print(f"   ❌ 浏览器未在列表中找到")
                return False
        else:
            print(f"   ❌ 测试浏览器创建失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 后端重命名测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_scenarios():
    """测试错误场景"""
    print("\n🛡️ 测试错误场景")
    print("-" * 30)
    
    try:
        from 浏览器管理器 import BrowserManager
        manager = BrowserManager()
        
        # 创建测试浏览器
        test_name1 = "错误测试浏览器1"
        test_name2 = "错误测试浏览器2"
        
        # 先清理
        for name in [test_name1, test_name2]:
            browsers = manager.list_browsers()
            for browser in browsers:
                if browser['name'] == name:
                    manager.delete_browser(name)
        
        # 创建两个测试浏览器
        manager.create_browser(test_name1)
        manager.create_browser(test_name2)
        
        print("1. 测试重命名为已存在的名称")
        if not manager.rename_browser(test_name1, test_name2):
            print("   ✅ 正确拒绝重命名为已存在的名称")
        else:
            print("   ❌ 应该拒绝重命名为已存在的名称")
        
        print("2. 测试重命名不存在的浏览器")
        if not manager.rename_browser("不存在的浏览器", "新名称"):
            print("   ✅ 正确拒绝重命名不存在的浏览器")
        else:
            print("   ❌ 应该拒绝重命名不存在的浏览器")
        
        print("3. 测试无效的新名称")
        invalid_names = ["", "   ", "浏览器<>", "浏览器|", "浏览器?"]
        for invalid_name in invalid_names:
            if not manager.rename_browser(test_name1, invalid_name):
                print(f"   ✅ 正确拒绝无效名称: '{invalid_name}'")
            else:
                print(f"   ❌ 应该拒绝无效名称: '{invalid_name}'")
        
        # 清理测试浏览器
        manager.delete_browser(test_name1)
        manager.delete_browser(test_name2)
        
        return True
        
    except Exception as e:
        print(f"❌ 错误场景测试失败: {e}")
        return False

def test_gui_rename():
    """测试GUI重命名功能"""
    print("\n🖥️ 测试GUI重命名功能")
    print("-" * 30)
    
    try:
        from 浏览器管理器GUI import BrowserManagerGUI
        
        # 创建GUI实例
        app = BrowserManagerGUI()
        
        # 检查重命名按钮是否存在
        if hasattr(app, 'rename_btn'):
            print("✅ 重命名按钮存在")
        else:
            print("❌ 重命名按钮不存在")
            app.root.destroy()
            return False
        
        # 检查重命名方法是否存在
        if hasattr(app, 'rename_browser') and callable(app.rename_browser):
            print("✅ 重命名方法存在")
        else:
            print("❌ 重命名方法不存在")
            app.root.destroy()
            return False
        
        # 检查重命名对话框方法是否存在
        if hasattr(app, 'show_rename_dialog') and callable(app.show_rename_dialog):
            print("✅ 重命名对话框方法存在")
        else:
            print("❌ 重命名对话框方法不存在")
            app.root.destroy()
            return False
        
        # 销毁GUI
        app.root.destroy()
        print("✅ GUI重命名功能检查完成")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI重命名测试失败: {e}")
        return False

def test_integration():
    """测试集成功能"""
    print("\n🔗 测试集成功能")
    print("-" * 25)
    
    try:
        from 浏览器管理器 import BrowserManager
        from 快捷方式管理器 import shortcut_manager
        from 桌面图标管理器 import desktop_icon_manager
        
        manager = BrowserManager()
        
        # 创建测试浏览器
        test_name = "集成测试浏览器"
        new_name = "集成测试重命名后"
        
        # 清理
        for name in [test_name, new_name]:
            browsers = manager.list_browsers()
            for browser in browsers:
                if browser['name'] == name:
                    manager.delete_browser(name)
        
        # 创建测试浏览器
        if manager.create_browser(test_name):
            print(f"✅ 测试浏览器创建成功: {test_name}")
            
            # 创建桌面快捷方式
            if shortcut_manager.create_desktop_shortcut(test_name):
                print(f"✅ 桌面快捷方式创建成功")
                
                # 执行重命名
                if manager.rename_browser(test_name, new_name):
                    print(f"✅ 浏览器重命名成功: {test_name} → {new_name}")
                    
                    # 检查桌面快捷方式是否更新
                    desktop_path = shortcut_manager._get_desktop_path()
                    if desktop_path:
                        old_shortcut = desktop_path / f"{test_name}.lnk"
                        new_shortcut = desktop_path / f"{new_name}.lnk"
                        
                        if not old_shortcut.exists() and new_shortcut.exists():
                            print(f"✅ 桌面快捷方式更新成功")
                        else:
                            print(f"⚠️ 桌面快捷方式更新可能失败")
                    
                    # 清理
                    shortcut_manager.delete_desktop_shortcut(new_name)
                    manager.delete_browser(new_name)
                    print(f"✅ 测试数据清理完成")
                    
                else:
                    print(f"❌ 浏览器重命名失败")
                    return False
            else:
                print(f"⚠️ 桌面快捷方式创建失败，跳过集成测试")
                manager.delete_browser(test_name)
                return True
        else:
            print(f"❌ 测试浏览器创建失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 重命名功能全面测试")
    print("=" * 50)
    
    tests = [
        ("后端重命名功能", test_backend_rename),
        ("错误场景处理", test_error_scenarios),
        ("GUI重命名功能", test_gui_rename),
        ("集成功能测试", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
        
        time.sleep(0.5)  # 短暂延迟
    
    print("\n" + "=" * 50)
    print(f"📊 重命名功能测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 重命名功能测试完全通过！")
        print("\n✅ 重命名功能验证:")
        print("   - ✅ 后端重命名逻辑正确")
        print("   - ✅ 目录和文件重命名正常")
        print("   - ✅ 启动脚本更新正确")
        print("   - ✅ 桌面快捷方式更新正常")
        print("   - ✅ 错误处理机制完善")
        print("   - ✅ GUI界面集成正常")
        print("   - ✅ 各模块协同工作正常")
        print("\n🚀 重命名功能完全可以投入使用！")
        return True
    else:
        print("⚠️ 部分重命名功能存在问题，需要修复")
        return False

if __name__ == "__main__":
    main()
