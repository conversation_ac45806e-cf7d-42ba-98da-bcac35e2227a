#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🎯 重命名功能快速演示")
print("=" * 40)

from 浏览器管理器 import BrowserManager
manager = BrowserManager()

# 显示现有浏览器
browsers = manager.list_browsers()
print(f"当前浏览器: {len(browsers)}个")

# 创建演示浏览器
demo_name = "演示浏览器"
new_name = "重命名后的演示浏览器"

# 清理
for b in browsers:
    if b['name'] in [demo_name, new_name]:
        manager.delete_browser(b['name'])

print(f"创建演示浏览器: {demo_name}")
if manager.create_browser(demo_name):
    print("✅ 创建成功")
    
    print(f"执行重命名: {demo_name} → {new_name}")
    if manager.rename_browser(demo_name, new_name):
        print("✅ 重命名成功")
        
        # 验证
        updated = manager.list_browsers()
        found = any(b['name'] == new_name for b in updated)
        print(f"验证结果: {'✅ 成功' if found else '❌ 失败'}")
        
        # 清理
        manager.delete_browser(new_name)
        print("✅ 清理完成")
    else:
        print("❌ 重命名失败")
        manager.delete_browser(demo_name)
else:
    print("❌ 创建失败")

print("🎉 演示完成")
