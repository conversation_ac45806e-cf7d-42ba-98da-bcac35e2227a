#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器测试程序
测试主题切换、颜色应用、配置保存等功能

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
from pathlib import Path

# 导入主题管理器
try:
    from 主题管理器 import theme_manager, apply_theme, switch_theme, get_theme_color
except ImportError:
    print("❌ 无法导入主题管理器，请确保主题管理器.py文件存在")
    sys.exit(1)

class ThemeTestApp:
    """主题测试应用"""
    
    def __init__(self):
        """初始化测试应用"""
        self.root = tk.Tk()
        self.root.title("🎨 主题管理器测试 v2.2.1")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        self.setup_ui()
        self.apply_current_theme()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = tk.Label(main_frame, text="🎨 主题管理器测试", font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 主题选择区域
        theme_frame = tk.LabelFrame(main_frame, text="主题选择", font=("Microsoft YaHei", 10))
        theme_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 当前主题显示
        current_theme_frame = tk.Frame(theme_frame)
        current_theme_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(current_theme_frame, text="当前主题:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT)
        self.current_theme_label = tk.Label(current_theme_frame, text="", font=("Microsoft YaHei", 9, "bold"))
        self.current_theme_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 主题切换按钮
        button_frame = tk.Frame(theme_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.modern_btn = tk.Button(button_frame, text="🌟 现代蓝色主题", 
                                   command=lambda: self.switch_to_theme("modern_blue"))
        self.modern_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.dark_btn = tk.Button(button_frame, text="🌙 深色主题", 
                                 command=lambda: self.switch_to_theme("dark_theme"))
        self.dark_btn.pack(side=tk.LEFT)
        
        # 组件测试区域
        test_frame = tk.LabelFrame(main_frame, text="组件测试", font=("Microsoft YaHei", 10))
        test_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 文本输入
        input_frame = tk.Frame(test_frame)
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(input_frame, text="文本输入:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT)
        self.entry = tk.Entry(input_frame, width=30)
        self.entry.pack(side=tk.LEFT, padx=(5, 0))
        self.entry.insert(0, "测试文本输入")
        
        # 列表框
        list_frame = tk.Frame(test_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        tk.Label(list_frame, text="列表组件:", font=("Microsoft YaHei", 9)).pack(anchor=tk.W)
        
        self.listbox = tk.Listbox(list_frame, height=6)
        self.listbox.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 添加测试数据
        test_items = [
            "🆕 创建新浏览器",
            "🚀 启动浏览器",
            "🔄 同步插件",
            "🎨 设置图标",
            "🖥️ 发送到桌面",
            "🗑️ 删除浏览器"
        ]
        for item in test_items:
            self.listbox.insert(tk.END, item)
        
        # 文本区域
        text_frame = tk.Frame(test_frame)
        text_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(text_frame, text="文本区域:", font=("Microsoft YaHei", 9)).pack(anchor=tk.W)
        
        self.text_area = tk.Text(text_frame, height=4, wrap=tk.WORD)
        self.text_area.pack(fill=tk.X, pady=(5, 0))
        self.text_area.insert("1.0", "这是一个多行文本区域，用于测试主题在文本组件上的效果。\n支持多行文本显示和编辑功能。")
        
        # 状态栏
        status_frame = tk.Frame(main_frame)
        status_frame.pack(fill=tk.X)
        
        self.status_label = tk.Label(status_frame, text="就绪", font=("Microsoft YaHei", 8))
        self.status_label.pack(side=tk.LEFT)
        
        # 颜色信息显示
        self.color_info_label = tk.Label(status_frame, text="", font=("Microsoft YaHei", 8))
        self.color_info_label.pack(side=tk.RIGHT)
        
        # 存储所有需要应用主题的组件
        self.themed_widgets = [
            (main_frame, "frame"),
            (title_label, "label"),
            (theme_frame, "frame"),
            (current_theme_frame, "frame"),
            (button_frame, "frame"),
            (self.current_theme_label, "label"),
            (self.modern_btn, "button"),
            (self.dark_btn, "button"),
            (test_frame, "frame"),
            (input_frame, "frame"),
            (self.entry, "entry"),
            (list_frame, "frame"),
            (self.listbox, "listbox"),
            (text_frame, "frame"),
            (self.text_area, "text"),
            (status_frame, "frame"),
            (self.status_label, "label"),
            (self.color_info_label, "label")
        ]
    
    def switch_to_theme(self, theme_name: str):
        """切换到指定主题"""
        try:
            if switch_theme(theme_name):
                self.apply_current_theme()
                self.update_status(f"✅ 已切换到 {theme_manager.get_theme_info(theme_name)['name']}")
            else:
                self.update_status("❌ 主题切换失败")
                messagebox.showerror("错误", "主题切换失败，请检查日志")
        except Exception as e:
            self.update_status(f"❌ 主题切换错误: {e}")
            messagebox.showerror("错误", f"主题切换发生错误: {e}")
    
    def apply_current_theme(self):
        """应用当前主题"""
        try:
            # 应用窗口主题
            theme_manager.apply_theme_to_window(self.root)
            
            # 应用到所有组件
            for widget, widget_type in self.themed_widgets:
                apply_theme(widget, widget_type)
            
            # 更新主题信息显示
            current_theme = theme_manager.get_current_theme()
            theme_info = theme_manager.get_theme_info(current_theme)
            self.current_theme_label.config(text=theme_info['name'])
            
            # 更新颜色信息
            primary_color = get_theme_color('primary')
            bg_color = get_theme_color('bg_primary')
            self.color_info_label.config(text=f"主色: {primary_color} | 背景: {bg_color}")
            
            # 强制刷新界面
            self.root.update()
            
        except Exception as e:
            print(f"❌ 应用主题失败: {e}")
    
    def update_status(self, message: str):
        """更新状态栏"""
        self.status_label.config(text=message)
        self.root.after(3000, lambda: self.status_label.config(text="就绪"))
    
    def run(self):
        """运行测试应用"""
        print("🎨 启动主题管理器测试应用...")
        print(f"当前主题: {theme_manager.get_current_theme()}")
        print("💡 使用界面按钮测试主题切换功能")
        
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = ThemeTestApp()
        app.run()
    except Exception as e:
        print(f"❌ 测试应用启动失败: {e}")
        messagebox.showerror("错误", f"测试应用启动失败: {e}")

if __name__ == "__main__":
    main()
