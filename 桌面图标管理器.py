#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 桌面图标管理器
专门管理桌面图标的创建、更新、删除和组织

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import time
from datetime import datetime

# 导入相关模块
try:
    from 配置管理器 import config_manager
    from 浏览器管理器 import BrowserManager
    from 快捷方式管理器 import shortcut_manager
    from 图标管理器 import icon_manager
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

class DesktopIconManager:
    """桌面图标管理器 - 专门管理桌面图标"""
    
    def __init__(self):
        """初始化桌面图标管理器"""
        self.config = config_manager
        self.browser_manager = BrowserManager()
        self.project_root = self.config.get_project_root()
        
        # 桌面图标记录
        self.desktop_icons = {}
        self.icon_groups = {}
        
        self._setup_logging()
        self._load_desktop_icons()
        
        self.logger.info("桌面图标管理器初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.project_root / "日志"
        log_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger("DesktopIconManager")
        if not self.logger.handlers:
            handler = logging.FileHandler(log_dir / "桌面图标管理器.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _load_desktop_icons(self):
        """加载桌面图标记录"""
        try:
            icons_file = self.project_root / "桌面图标记录.json"
            if icons_file.exists():
                with open(icons_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.desktop_icons = data.get('icons', {})
                    self.icon_groups = data.get('groups', {})
                self.logger.info(f"桌面图标记录加载成功: {len(self.desktop_icons)}个图标")
            else:
                self.desktop_icons = {}
                self.icon_groups = {}
                self.logger.info("创建新的桌面图标记录")
        except Exception as e:
            self.logger.error(f"加载桌面图标记录失败: {e}")
            self.desktop_icons = {}
            self.icon_groups = {}
    
    def _save_desktop_icons(self):
        """保存桌面图标记录"""
        try:
            icons_file = self.project_root / "桌面图标记录.json"
            data = {
                'icons': self.desktop_icons,
                'groups': self.icon_groups,
                'last_updated': datetime.now().isoformat()
            }
            with open(icons_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.info("桌面图标记录保存成功")
        except Exception as e:
            self.logger.error(f"保存桌面图标记录失败: {e}")
    
    def scan_desktop_shortcuts(self) -> Dict[str, Dict]:
        """扫描桌面上的快捷方式"""
        try:
            desktop_path = shortcut_manager._get_desktop_path()
            if not desktop_path:
                self.logger.error("无法获取桌面路径")
                return {}
            
            shortcuts = {}
            
            # 扫描.lnk文件
            for shortcut_file in desktop_path.glob("*.lnk"):
                shortcut_name = shortcut_file.stem
                
                # 检查是否是我们创建的浏览器快捷方式
                is_browser_shortcut = False
                browser_name = None
                
                browsers = self.browser_manager.list_browsers()
                for browser in browsers:
                    if browser['name'] == shortcut_name:
                        is_browser_shortcut = True
                        browser_name = browser['name']
                        break
                
                shortcuts[shortcut_name] = {
                    "path": str(shortcut_file),
                    "is_browser_shortcut": is_browser_shortcut,
                    "browser_name": browser_name,
                    "size": shortcut_file.stat().st_size,
                    "created_time": shortcut_file.stat().st_ctime,
                    "modified_time": shortcut_file.stat().st_mtime
                }
            
            self.logger.info(f"桌面快捷方式扫描完成: {len(shortcuts)}个")
            return shortcuts
            
        except Exception as e:
            self.logger.error(f"扫描桌面快捷方式失败: {e}")
            return {}
    
    def create_browser_desktop_icon(self, browser_name: str, icon_path: Optional[str] = None, 
                                   group: Optional[str] = None) -> bool:
        """
        创建浏览器桌面图标
        
        Args:
            browser_name: 浏览器名称
            icon_path: 自定义图标路径
            group: 图标分组
            
        Returns:
            是否创建成功
        """
        try:
            # 检查浏览器是否存在
            browsers = self.browser_manager.list_browsers()
            browser_exists = any(b['name'] == browser_name for b in browsers)
            
            if not browser_exists:
                self.logger.error(f"浏览器不存在: {browser_name}")
                return False
            
            # 创建快捷方式
            success = shortcut_manager.create_desktop_shortcut(browser_name, icon_path)
            
            if success:
                # 记录图标信息
                icon_info = {
                    "browser_name": browser_name,
                    "icon_path": icon_path,
                    "group": group or "默认",
                    "created_time": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                }
                
                self.desktop_icons[browser_name] = icon_info
                
                # 更新分组
                if group:
                    if group not in self.icon_groups:
                        self.icon_groups[group] = []
                    if browser_name not in self.icon_groups[group]:
                        self.icon_groups[group].append(browser_name)
                
                self._save_desktop_icons()
                self.logger.info(f"桌面图标创建成功: {browser_name}")
                return True
            else:
                self.logger.error(f"桌面图标创建失败: {browser_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"创建桌面图标失败: {browser_name} - {e}")
            return False
    
    def update_browser_icon(self, browser_name: str, new_icon_path: str) -> bool:
        """
        更新浏览器图标
        
        Args:
            browser_name: 浏览器名称
            new_icon_path: 新图标路径
            
        Returns:
            是否更新成功
        """
        try:
            # 更新快捷方式图标
            success = shortcut_manager.update_shortcut_icon(browser_name, new_icon_path)
            
            if success:
                # 更新记录
                if browser_name in self.desktop_icons:
                    self.desktop_icons[browser_name]["icon_path"] = new_icon_path
                    self.desktop_icons[browser_name]["last_updated"] = datetime.now().isoformat()
                else:
                    # 创建新记录
                    self.desktop_icons[browser_name] = {
                        "browser_name": browser_name,
                        "icon_path": new_icon_path,
                        "group": "默认",
                        "created_time": datetime.now().isoformat(),
                        "last_updated": datetime.now().isoformat()
                    }
                
                self._save_desktop_icons()
                self.logger.info(f"桌面图标更新成功: {browser_name}")
                return True
            else:
                self.logger.error(f"桌面图标更新失败: {browser_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"更新桌面图标失败: {browser_name} - {e}")
            return False
    
    def remove_browser_desktop_icon(self, browser_name: str) -> bool:
        """
        删除浏览器桌面图标
        
        Args:
            browser_name: 浏览器名称
            
        Returns:
            是否删除成功
        """
        try:
            # 删除快捷方式
            success = shortcut_manager.delete_desktop_shortcut(browser_name)
            
            if success:
                # 从记录中删除
                if browser_name in self.desktop_icons:
                    icon_info = self.desktop_icons[browser_name]
                    group = icon_info.get("group")
                    
                    # 从分组中删除
                    if group and group in self.icon_groups:
                        if browser_name in self.icon_groups[group]:
                            self.icon_groups[group].remove(browser_name)
                        
                        # 如果分组为空，删除分组
                        if not self.icon_groups[group]:
                            del self.icon_groups[group]
                    
                    del self.desktop_icons[browser_name]
                    self._save_desktop_icons()
                
                self.logger.info(f"桌面图标删除成功: {browser_name}")
                return True
            else:
                self.logger.warning(f"桌面图标删除失败: {browser_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除桌面图标失败: {browser_name} - {e}")
            return False
    
    def batch_create_icons(self, browser_names: List[str], group: Optional[str] = None) -> Dict[str, bool]:
        """
        批量创建桌面图标
        
        Args:
            browser_names: 浏览器名称列表
            group: 图标分组
            
        Returns:
            创建结果
        """
        results = {}
        
        for browser_name in browser_names:
            try:
                # 获取浏览器图标
                browser_icon = icon_manager.get_browser_icon(browser_name)
                
                success = self.create_browser_desktop_icon(browser_name, browser_icon, group)
                results[browser_name] = success
                
                # 短暂延迟，避免操作过快
                time.sleep(0.2)
                
            except Exception as e:
                self.logger.error(f"批量创建图标失败: {browser_name} - {e}")
                results[browser_name] = False
        
        success_count = sum(results.values())
        self.logger.info(f"批量创建完成: {success_count}/{len(browser_names)} 成功")
        
        return results
    
    def organize_icons_by_group(self, group_configs: Dict[str, List[str]]) -> bool:
        """
        按分组组织图标
        
        Args:
            group_configs: 分组配置 {分组名: [浏览器名列表]}
            
        Returns:
            是否组织成功
        """
        try:
            # 更新分组配置
            self.icon_groups.update(group_configs)
            
            # 更新图标记录中的分组信息
            for group_name, browser_names in group_configs.items():
                for browser_name in browser_names:
                    if browser_name in self.desktop_icons:
                        self.desktop_icons[browser_name]["group"] = group_name
                        self.desktop_icons[browser_name]["last_updated"] = datetime.now().isoformat()
            
            self._save_desktop_icons()
            self.logger.info(f"图标分组组织完成: {len(group_configs)}个分组")
            return True
            
        except Exception as e:
            self.logger.error(f"组织图标分组失败: {e}")
            return False
    
    def get_desktop_icon_status(self) -> Dict[str, Dict]:
        """获取桌面图标状态"""
        try:
            status = {
                "managed_icons": self.desktop_icons.copy(),
                "groups": self.icon_groups.copy(),
                "desktop_shortcuts": self.scan_desktop_shortcuts(),
                "statistics": {
                    "total_managed": len(self.desktop_icons),
                    "total_groups": len(self.icon_groups),
                    "total_desktop_shortcuts": 0
                }
            }
            
            # 统计桌面快捷方式
            desktop_shortcuts = status["desktop_shortcuts"]
            status["statistics"]["total_desktop_shortcuts"] = len(desktop_shortcuts)
            status["statistics"]["browser_shortcuts"] = len([s for s in desktop_shortcuts.values() if s["is_browser_shortcut"]])
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取桌面图标状态失败: {e}")
            return {}
    
    def cleanup_orphaned_shortcuts(self) -> int:
        """清理孤立的快捷方式"""
        try:
            desktop_shortcuts = self.scan_desktop_shortcuts()
            browsers = {b['name'] for b in self.browser_manager.list_browsers()}
            
            cleaned_count = 0
            
            for shortcut_name, shortcut_info in desktop_shortcuts.items():
                if shortcut_info["is_browser_shortcut"] and shortcut_name not in browsers:
                    # 这是一个孤立的浏览器快捷方式
                    try:
                        shortcut_path = Path(shortcut_info["path"])
                        shortcut_path.unlink()
                        
                        # 从记录中删除
                        if shortcut_name in self.desktop_icons:
                            del self.desktop_icons[shortcut_name]
                        
                        cleaned_count += 1
                        self.logger.info(f"清理孤立快捷方式: {shortcut_name}")
                        
                    except Exception as e:
                        self.logger.warning(f"清理快捷方式失败: {shortcut_name} - {e}")
            
            if cleaned_count > 0:
                self._save_desktop_icons()
            
            self.logger.info(f"孤立快捷方式清理完成: {cleaned_count}个")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理孤立快捷方式失败: {e}")
            return 0
    
    def export_icon_config(self, export_path: Optional[Path] = None) -> bool:
        """导出图标配置"""
        try:
            if export_path is None:
                export_path = self.project_root / "桌面图标配置导出.json"
            
            config = {
                "export_time": datetime.now().isoformat(),
                "desktop_icons": self.desktop_icons,
                "icon_groups": self.icon_groups,
                "version": "v2.2.1"
            }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"图标配置导出成功: {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出图标配置失败: {e}")
            return False
    
    def import_icon_config(self, import_path: Path) -> bool:
        """导入图标配置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.desktop_icons = config.get("desktop_icons", {})
            self.icon_groups = config.get("icon_groups", {})
            
            self._save_desktop_icons()
            self.logger.info(f"图标配置导入成功: {import_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导入图标配置失败: {e}")
            return False

# 全局桌面图标管理器实例
desktop_icon_manager = DesktopIconManager()

def create_desktop_icon(browser_name: str, icon_path: Optional[str] = None, group: Optional[str] = None) -> bool:
    """快捷函数：创建桌面图标"""
    return desktop_icon_manager.create_browser_desktop_icon(browser_name, icon_path, group)

def remove_desktop_icon(browser_name: str) -> bool:
    """快捷函数：删除桌面图标"""
    return desktop_icon_manager.remove_browser_desktop_icon(browser_name)

def get_desktop_status() -> Dict[str, Dict]:
    """快捷函数：获取桌面状态"""
    return desktop_icon_manager.get_desktop_icon_status()

if __name__ == "__main__":
    # 测试桌面图标管理器
    print("🖥️ 桌面图标管理器测试")
    
    # 获取桌面状态
    status = get_desktop_status()
    stats = status.get('statistics', {})
    print(f"桌面状态: {stats}")
    
    # 扫描桌面快捷方式
    shortcuts = desktop_icon_manager.scan_desktop_shortcuts()
    browser_shortcuts = [s for s in shortcuts.values() if s["is_browser_shortcut"]]
    print(f"桌面快捷方式: {len(shortcuts)}个 (浏览器: {len(browser_shortcuts)}个)")
    
    # 清理孤立快捷方式
    cleaned = desktop_icon_manager.cleanup_orphaned_shortcuts()
    print(f"清理孤立快捷方式: {cleaned}个")
    
    print("✅ 桌面图标管理器测试完成")
