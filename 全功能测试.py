#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 全功能测试
测试所有已开发的功能模块

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import sys
from pathlib import Path

def test_all_modules():
    """测试所有模块导入"""
    print("📦 测试所有模块导入")
    
    modules = [
        ("配置管理器", "from 配置管理器 import config_manager"),
        ("主题管理器", "from 主题管理器 import theme_manager"),
        ("浏览器管理器", "from 浏览器管理器 import BrowserManager"),
        ("快捷方式管理器", "from 快捷方式管理器 import shortcut_manager"),
        ("图标管理器", "from 图标管理器 import icon_manager"),
        ("默认图标下载器", "from 默认图标下载器 import default_icon_downloader"),
        ("插件同步管理器", "from 插件同步管理器 import plugin_sync_manager"),
        ("桌面图标管理器", "from 桌面图标管理器 import desktop_icon_manager"),
        ("GUI管理器", "from 浏览器管理器GUI import BrowserManagerGUI")
    ]
    
    success_count = 0
    for module_name, import_statement in modules:
        try:
            exec(import_statement)
            print(f"✅ {module_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}: {e}")
    
    print(f"模块导入结果: {success_count}/{len(modules)} 成功")
    return success_count == len(modules)

def test_icon_management():
    """测试图标管理功能"""
    print("\n🎨 测试图标管理功能")
    
    try:
        from 图标管理器 import icon_manager, get_available_icons
        from 默认图标下载器 import default_icon_downloader, get_default_icons
        
        # 测试本地图标获取
        local_icons = get_available_icons()
        print(f"✅ 本地图标: {len(local_icons)}个")
        
        # 测试默认图标定义
        default_icons = get_default_icons()
        print(f"✅ 默认图标定义: {len(default_icons)}个")
        
        # 测试图标搜索
        search_results = default_icon_downloader.search_icons("chrome")
        print(f"✅ 图标搜索: 找到{len(search_results)}个匹配")
        
        # 测试图标状态
        status = default_icon_downloader.get_download_status()
        existing_count = sum(1 for s in status.values() if s['exists'])
        print(f"✅ 图标状态: {existing_count}/{len(status)}个已存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 图标管理测试失败: {e}")
        return False

def test_plugin_sync():
    """测试插件同步功能"""
    print("\n🔄 测试插件同步功能")
    
    try:
        from 插件同步管理器 import plugin_sync_manager, generate_matrix
        from 浏览器管理器 import BrowserManager
        
        # 获取浏览器列表
        browser_manager = BrowserManager()
        browsers = browser_manager.list_browsers()
        print(f"✅ 可用浏览器: {len(browsers)}个")
        
        if browsers:
            # 测试插件扫描
            test_browser = browsers[0]['name']
            plugins = plugin_sync_manager.scan_browser_plugins(test_browser)
            print(f"✅ 插件扫描: {test_browser} 有{len(plugins)}个插件")
            
            # 测试插件矩阵
            matrix = generate_matrix()
            if matrix:
                stats = matrix.get('statistics', {})
                print(f"✅ 插件矩阵: {stats}")
            else:
                print("⚠️ 插件矩阵为空")
        
        # 测试同步历史
        history = plugin_sync_manager.get_sync_history(5)
        print(f"✅ 同步历史: {len(history)}条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 插件同步测试失败: {e}")
        return False

def test_desktop_management():
    """测试桌面管理功能"""
    print("\n🖥️ 测试桌面管理功能")
    
    try:
        from 桌面图标管理器 import desktop_icon_manager, get_desktop_status
        
        # 测试桌面状态
        status = get_desktop_status()
        stats = status.get('statistics', {})
        print(f"✅ 桌面状态: {stats}")
        
        # 测试快捷方式扫描
        shortcuts = desktop_icon_manager.scan_desktop_shortcuts()
        browser_shortcuts = [s for s in shortcuts.values() if s["is_browser_shortcut"]]
        print(f"✅ 桌面扫描: {len(shortcuts)}个快捷方式 (浏览器: {len(browser_shortcuts)}个)")
        
        # 测试孤立快捷方式清理
        cleaned = desktop_icon_manager.cleanup_orphaned_shortcuts()
        print(f"✅ 清理孤立: {cleaned}个快捷方式")
        
        return True
        
    except Exception as e:
        print(f"❌ 桌面管理测试失败: {e}")
        return False

def test_integration():
    """测试模块集成"""
    print("\n🔗 测试模块集成")
    
    try:
        from 浏览器管理器 import BrowserManager
        from 图标管理器 import icon_manager
        from 快捷方式管理器 import shortcut_manager
        from 桌面图标管理器 import desktop_icon_manager
        
        # 测试浏览器-图标集成
        browser_manager = BrowserManager()
        browsers = browser_manager.list_browsers()
        
        if browsers:
            test_browser = browsers[0]['name']
            
            # 测试获取浏览器图标
            browser_icon = icon_manager.get_browser_icon(test_browser)
            if browser_icon:
                print(f"✅ 浏览器图标集成: {test_browser} 有图标")
            else:
                print(f"⚠️ 浏览器图标集成: {test_browser} 无图标")
            
            # 测试桌面图标管理集成
            desktop_status = desktop_icon_manager.get_desktop_icon_status()
            managed_icons = desktop_status.get('managed_icons', {})
            print(f"✅ 桌面集成: 管理{len(managed_icons)}个图标")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块集成测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    print("\n⚡ 测试性能")
    
    try:
        import time
        from 图标管理器 import get_available_icons
        from 插件同步管理器 import generate_matrix
        from 桌面图标管理器 import get_desktop_status
        
        # 测试图标获取性能
        start_time = time.time()
        icons = get_available_icons()
        icon_time = time.time() - start_time
        print(f"✅ 图标获取: {icon_time:.3f}秒 ({len(icons)}个)")
        
        # 测试插件矩阵性能
        start_time = time.time()
        matrix = generate_matrix()
        matrix_time = time.time() - start_time
        print(f"✅ 插件矩阵: {matrix_time:.3f}秒")
        
        # 测试桌面状态性能
        start_time = time.time()
        status = get_desktop_status()
        status_time = time.time() - start_time
        print(f"✅ 桌面状态: {status_time:.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构")
    
    try:
        from 配置管理器 import config_manager
        
        project_root = config_manager.get_project_root()
        
        # 检查所有核心文件
        core_files = [
            "启动管理器.py",
            "浏览器管理器.py",
            "浏览器管理器GUI.py",
            "配置管理器.py",
            "主题管理器.py",
            "快捷方式管理器.py",
            "图标管理器.py",
            "默认图标下载器.py",
            "插件同步管理器.py",
            "桌面图标管理器.py",
            "项目配置.json",
            "browsers.cognigraph.json",
            "requirements.txt"
        ]
        
        existing_files = []
        missing_files = []
        
        for file_name in core_files:
            file_path = project_root / file_name
            if file_path.exists():
                existing_files.append(file_name)
            else:
                missing_files.append(file_name)
        
        print(f"✅ 核心文件: {len(existing_files)}/{len(core_files)}个存在")
        
        if missing_files:
            print(f"❌ 缺失文件: {missing_files}")
            return False
        
        # 检查目录结构
        required_dirs = [
            "浏览器实例",
            "默认图标",
            "日志",
            "GoogleChromePortable"
        ]
        
        existing_dirs = []
        missing_dirs = []
        
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            if dir_path.exists():
                existing_dirs.append(dir_name)
            else:
                missing_dirs.append(dir_name)
        
        print(f"✅ 必需目录: {len(existing_dirs)}/{len(required_dirs)}个存在")
        
        if missing_dirs:
            print(f"❌ 缺失目录: {missing_dirs}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件结构测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 浏览器多账号绿色版 v2.2.1 - 全功能测试")
    print("=" * 70)
    
    tests = [
        ("模块导入", test_all_modules),
        ("文件结构", test_file_structure),
        ("图标管理", test_icon_management),
        ("插件同步", test_plugin_sync),
        ("桌面管理", test_desktop_management),
        ("模块集成", test_integration),
        ("性能测试", test_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 全功能测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！系统完全正常！")
        print("\n🚀 浏览器多账号绿色版 v2.2.1 开发完成！")
        print("💡 使用方法: python 启动管理器.py")
        print("📚 功能特性:")
        print("   - 🖥️ 现代化GUI界面")
        print("   - 🎨 双主题支持")
        print("   - 🔗 智能快捷方式管理")
        print("   - 🎯 专业图标管理")
        print("   - 🔄 插件同步功能")
        print("   - 🖥️ 桌面图标管理")
        print("   - ⚙️ 完整配置系统")
        print("   - 📝 详细日志记录")
        return True
    else:
        print("⚠️ 部分功能测试未通过，请检查上述结果")
        return False

if __name__ == "__main__":
    main()
