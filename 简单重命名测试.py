#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("开始重命名功能测试...")

try:
    from 浏览器管理器 import BrowserManager
    print("✅ 浏览器管理器导入成功")
    
    manager = BrowserManager()
    print("✅ 浏览器管理器创建成功")
    
    # 测试重命名方法是否存在
    if hasattr(manager, 'rename_browser'):
        print("✅ 重命名方法存在")
    else:
        print("❌ 重命名方法不存在")
    
    # 列出现有浏览器
    browsers = manager.list_browsers()
    print(f"现有浏览器: {len(browsers)}个")
    for b in browsers:
        print(f"  - {b['name']}")
    
    # 创建测试浏览器
    test_name = "重命名测试浏览器"
    new_name = "重命名后的浏览器"
    
    # 清理可能存在的测试浏览器
    for browser in browsers:
        if browser['name'] in [test_name, new_name]:
            manager.delete_browser(browser['name'])
            print(f"清理已存在的浏览器: {browser['name']}")
    
    # 创建测试浏览器
    print(f"创建测试浏览器: {test_name}")
    if manager.create_browser(test_name):
        print("✅ 测试浏览器创建成功")
        
        # 执行重命名
        print(f"重命名: {test_name} → {new_name}")
        if manager.rename_browser(test_name, new_name):
            print("✅ 重命名成功")
            
            # 验证重命名结果
            updated_browsers = manager.list_browsers()
            old_found = any(b['name'] == test_name for b in updated_browsers)
            new_found = any(b['name'] == new_name for b in updated_browsers)
            
            print(f"验证结果:")
            print(f"  旧名称存在: {old_found}")
            print(f"  新名称存在: {new_found}")
            
            if not old_found and new_found:
                print("✅ 重命名验证成功")
            else:
                print("❌ 重命名验证失败")
            
            # 清理测试浏览器
            manager.delete_browser(new_name)
            print("✅ 测试浏览器清理完成")
            
        else:
            print("❌ 重命名失败")
            manager.delete_browser(test_name)
    else:
        print("❌ 测试浏览器创建失败")
    
    # 测试GUI
    print("\n测试GUI重命名功能...")
    from 浏览器管理器GUI import BrowserManagerGUI
    app = BrowserManagerGUI()
    
    if hasattr(app, 'rename_btn'):
        print("✅ GUI重命名按钮存在")
    else:
        print("❌ GUI重命名按钮不存在")
    
    if hasattr(app, 'rename_browser'):
        print("✅ GUI重命名方法存在")
    else:
        print("❌ GUI重命名方法不存在")
    
    app.root.destroy()
    
    print("\n✅ 重命名功能测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
