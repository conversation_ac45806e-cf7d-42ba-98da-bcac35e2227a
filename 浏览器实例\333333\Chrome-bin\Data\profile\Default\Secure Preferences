{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "查找适用于Google Chrome的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\Downloads\\workspace\\browsers\\GoogleChromePortable\\App\\Chrome-bin\\138.0.7204.158\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Users\\<USER>\\Downloads\\workspace\\browsers\\GoogleChromePortable\\App\\Chrome-bin\\138.0.7204.158\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "kebpgmmmoiggnchlpamiefihdjiaikaf": {"account_extension_type": 0, "ack_prompt_count": 1, "active_permissions": {"api": ["contextMenus", "cookies", "downloads", "nativeMessaging"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 4097, "disable_reasons": [8192], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 3, "manifest": {"action": {"default_popup": "xdown_settings.html", "default_title": "XDown Manager"}, "background": {"service_worker": "xdown_worker.js"}, "current_locale": "zh_CN", "default_locale": "en", "description": "Internet Downloader enables you to download a desired item with an Internet Download Manager (xdown) application.", "homepage_url": "https://xdown.org", "host_permissions": ["<all_urls>"], "icons": {"128": "images/xdown_128.png", "16": "images/xdown_16.png", "24": "images/xdown_24.png", "32": "images/xdown_32.png", "48": "images/xdown_48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtcGN5MQZWH+7Gl+KNipxnI/q+CFI6AuZwOzFTMVCGx3NS3jOoPL2vebtV366oR8cSXRsHyZ8GzSSBy1rMkM+U+nLf7L1IXrhETlw7RBspJU9vC/HHZQkMiDM1q3kwKLmqpuzfPssh71UCczuOVh2gungvw/Xm+bf3bDmPrHCBy14mdrNyywfhQCKgFzCNW4DnPsPjGpNZQ2cylILY+JcH61rwaMIj56Znpe6iP38GfBAmKYjEMRo4/n1Ry6CDWTmh+xBMVIFPMUM8Ip/EjBLraSPhOvc34XIsoo+beV3nEG3PEG1q6YKnOKpiQJF0qe3AbtPpieXniFXkY6ddF4yKQIDAQAB", "manifest_version": 3, "name": "XDown", "permissions": ["cookies", "nativeMessaging", "downloads", "contextMenus"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "2.0.1", "web_accessible_resources": [{"matches": ["*://*/*"], "resources": ["index.html"]}]}, "path": "kebpgmmmoiggnchlpamiefihdjiaikaf\\2.0.1_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "ljglajjnnkapghbckkcmodicjhacbfhk": {"account_extension_type": 0, "ack_prompt_count": 1, "active_permissions": {"api": ["browsingData", "debugger", "nativeMessaging", "tabs", "webNavigation", "scripting"], "explicit_host": ["<all_urls>", "http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 9, "disable_reasons": [8192], "first_install_time": "*****************", "from_webstore": true, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 6, "manifest": {"background": {"service_worker": "background.js"}, "content_security_policy": {"extension_pages": "default-src 'self'"}, "current_locale": "zh_CN", "default_locale": "en", "description": "Add-on for enabling web automation. This web extension is compatible with Power Automate for desktop version 2.27 or later.", "host_permissions": ["<all_urls>", "http://*/*", "https://*/*"], "icons": {"32": "PAD-ico_32.png", "48": "PAD-ico_48.png", "56": "PAD-ico_56.png", "64": "PAD-ico_64.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqZpvqCA+fQPc94GYw+FzGAbSKndnFd/Z8LcKVOrZ9/ZcQfGjMJsXQKSSIwMUGok/Fw6MLtYj1qCV2m7xaOQyBYHeoPwSLsK5N5wdKH0JXF4UA9jRtVLbQ8jbw7X+/CC/h7p19FXm5MmJz36iHL9eaVbr+6xgaxK/q+Y/Tsfn4aCBhbEkxylK43ReQMR2RAxo2YDwkEg/Pc31ueniDRp32TDb9sS5lzvxSFVicokGRfMtVc5r/2FrkN99TSh+ki46Fn2Ou8Nx3pGvTvaxW+uxr4r0Apb0H07QatLIR+iv7rugvpHgZ+D3TEUUZ69zZyjBZ2ETFWgn8FlMy4m9pRAXJQIDAQAB", "manifest_version": 3, "minimum_chrome_version": "88.0", "name": "Microsoft Power Automate", "permissions": ["scripting", "debugger", "tabs", "browsingData", "nativeMessaging", "webNavigation"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "*********"}, "path": "ljglajjnnkapghbckkcmodicjhacbfhk\\*********_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\Downloads\\workspace\\browsers\\GoogleChromePortable\\App\\Chrome-bin\\138.0.7204.158\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mnpdbmgpebfihcndnpgdaihnkmloclkd": {"account_extension_type": 0, "ack_prompt_count": 1, "active_permissions": {"api": ["activeTab", "alarms", "contextMenus", "cookies", "nativeMessaging", "storage", "tabs", "tts", "scripting", "sidePanel"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "commands": {"advanced_select": {"suggested_key": "Alt+Q", "was_assigned": true}, "commands_modal": {"suggested_key": "Alt+K", "was_assigned": true}, "summary_page": {"suggested_key": "Alt+S", "was_assigned": true}, "web_translate": {"suggested_key": "Alt+T", "was_assigned": true}}, "content_settings": [], "creation_flags": 4097, "disable_reasons": [8192], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 3, "manifest": {"action": {"default_icon": {"128": "logo-round256.png", "16": "logo-round128.png", "32": "logo-round128.png", "48": "logo-round128.png"}, "default_title": "清言"}, "background": {"service_worker": "static/js/background.js"}, "commands": {"advanced_select": {"description": "多链接总结", "suggested_key": {"default": "Alt+Q"}}, "commands_modal": {"description": "快捷弹窗", "suggested_key": {"default": "Alt+K"}}, "summary_page": {"description": "页面总结", "suggested_key": {"default": "Alt+S"}}, "web_translate": {"description": "网页翻译", "suggested_key": {"default": "Alt+T"}}}, "content_scripts": [{"js": ["static/js/content.js"], "matches": ["<all_urls>"]}], "current_locale": "zh_CN", "default_locale": "zh_CN", "description": "你的AI全能助手，通过划线工具、多链接总结、站内高级检索、写作助手等，清言插件助您轻松应对各种网络浏览场景。", "host_permissions": ["<all_urls>"], "icons": {"128": "logo-round256.png", "16": "logo-round128.png", "32": "logo-round128.png", "48": "logo-round128.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyHSi5fKRTULFH2xGfRIwOIqFVdGoD1pREgodKWhtJp/Wl1ZZkQCgubIs53ovV5ZYkHZcrXTRyjoPSsX87JWexE9hzxlAZRi67gqA654/ufiCtnSvymu3RWL8k2gpCzkfkKOe1Z1vZLaTll/X6N4CraYoXtM0wAmn05y7BLVzTalTb7jWRQeyJKkl0Mg2Mc9otY04UkOeBJ3QMCGAv8dw8muHShHQMq7VYitekF5ssO+5elXUhX3zZDY88GhgsgeVbhVzRxLzRFKl31F1OaP6tWnbCBVqks5yNGMP6eLkVjlR624ewTbn0mtTfDurfVKR6B0jgdehv0FRZUt5UREa/QIDAQAB", "manifest_version": 3, "name": "智谱清言：ChatGLM & AutoGLM, 工作学习 AI 助手", "options_page": "options.html", "permissions": ["storage", "activeTab", "sidePanel", "contextMenus", "scripting", "tabs", "cookies", "tts", "alarms", "nativeMessaging"], "short_name": "清言", "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.24", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["*.css"]}, {"matches": ["<all_urls>"], "resources": ["*.js"]}, {"matches": ["<all_urls>"], "resources": ["static/css/*"]}, {"matches": ["<all_urls>"], "resources": ["prompts.json"]}, {"matches": ["<all_urls>"], "resources": ["static/media/*"]}]}, "path": "mnpdbmgpebfihcndnpgdaihnkmloclkd\\1.0.24_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "ncennffkjdiamlpmcbajkmaiiiddgioo": {"account_extension_type": 0, "ack_prompt_count": 1, "active_permissions": {"api": ["contextMenus", "cookies", "downloads", "nativeMessaging", "notifications", "storage", "tabs", "webRequest", "scripting"], "explicit_host": ["<all_urls>", "http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["ftp://*/*", "http://*/*", "https://*/*"]}, "commands": {}, "content_settings": [], "creation_flags": 4097, "disable_reasons": [8192], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 3, "manifest": {"action": {"default_icon": "assets/icon19_normal.png", "default_popup": "popup.html", "default_title": "迅雷Chrome支持"}, "background": {"service_worker": "service-worker-loader.js", "type": "module"}, "content_scripts": [{"all_frames": true, "css": ["assets/content.css", "assets/chrome-runtime-promise-71a4e0c4.css", "assets/content-292a538b.css"], "js": ["assets/content.js-loader-58705284.js"], "matches": ["http://*/*", "https://*/*", "ftp://*/*"], "run_at": "document_start"}], "current_locale": "zh_CN", "default_locale": "zh_CN", "description": "迅雷下载支持", "host_permissions": ["<all_urls>", "http://*/*", "https://*/*"], "icons": {"128": "assets/install_logo.png", "16": "assets/menu_logo.png", "48": "assets/extension_logo.png"}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDEa5DG04lhgzzm3gRSXPPOZOv6ZXnzQrBv+rjUE/dL5br9Duh1kbwGQJCO4QMDvD1usf6FoXDsuvZwYzH6lg1pLI7m/wmQC3NQURHQ7J5zAy7VY0F7qSVqclcpRKY2k00vcqxok6lota3Z1QxUVUwWc9VUfr4gRUeQa4KlEsXzGwIDAQAB", "manifest_version": 3, "name": "迅雷下载支持", "optional_permissions": [], "options_page": "options.html", "permissions": ["contextMenus", "cookies", "tabs", "webRequest", "downloads", "nativeMessaging", "storage", "scripting", "notifications"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "3.52.5", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["assets/*"], "use_dynamic_url": false}, {"matches": ["ftp://*/*", "http://*/*", "https://*/*"], "resources": ["assets/runtime-dom.esm-bundler-65457635.js", "assets/util-2d803e2a.js", "assets/index-7b1b919b.js", "assets/tool-aa30449a.js", "assets/chrome-runtime-promise-fcd5d99a.js", "assets/stat-b988e0dc.js", "assets/content.js-c1c147bd.js"], "use_dynamic_url": false}]}, "path": "ncennffkjdiamlpmcbajkmaiiiddgioo\\3.52.5_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Users\\<USER>\\Downloads\\workspace\\browsers\\GoogleChromePortable\\App\\Chrome-bin\\138.0.7204.158\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nmmhkkegccagdldgiimedpiccmgmieda": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 137, "disable_reasons": [], "events": ["app.runtime.onLaunched", "runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 10, "manifest": {"app": {"background": {"scripts": ["craw_background.js"]}}, "current_locale": "zh_CN", "default_locale": "en", "description": "Chrome 网上应用店付款系统", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon_128.png", "16": "images/icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrKfMnLqViEyokd1wk57FxJtW2XXpGXzIHBzv9vQI/01UsuP0IV5/lj0wx7zJ/xcibUgDeIxobvv9XD+zO1MdjMWuqJFcKuSS4Suqkje6u+pMrTSGOSHq1bmBVh0kpToN8YoJs/P/yrRd7FEtAXTaFTGxQL4C385MeXSjaQfiRiQIDAQAB", "manifest_version": 2, "minimum_chrome_version": "29", "name": "Chrome 网上应用店付款系统", "oauth2": {"auto_approve": true, "client_id": "203784468217.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/sierra", "https://www.googleapis.com/auth/sierrasandbox", "https://www.googleapis.com/auth/chromewebstore", "https://www.googleapis.com/auth/chromewebstore.readonly"]}, "permissions": ["identity", "webview", "https://www.google.com/", "https://www.googleapis.com/*", "https://payments.google.com/payments/v4/js/integrator.js", "https://sandbox.google.com/payments/v4/js/integrator.js"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.0.6"}, "path": "nmmhkkegccagdldgiimedpiccmgmieda\\1.0.0.6_0", "preferences": {}, "regular_only_preferences": {}, "running": true, "was_installed_by_default": true, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "42DD5CBC1629173CC8BECD0218102AA54DD2EA9A6318DD77A6A816A2A8468F12"}, "extensions": {"ui": {"developer_mode": "A465886E46184412869CD02A58AFE38EDD24686B57CA16F5B83981CADDCD4B28"}}, "homepage": "BE6028C4CE8BD9010B81357D11F2CE254FAA33D4F1CD4042CE100C01245EE814", "homepage_is_newtabpage": "018B85597EA211E359BC6108535EC0EDA725335506ACE3A15D1B158A25984461", "session": {"restore_on_startup": "24EFD2E1F8F6DD3B443C49EEA893E43EFD5CC91A2297DF1BE3EBDDC92306E0E6", "startup_urls": "086F36119E168FE064D670AF8402E5CA73ADD9292FB29D9E94E583A028CA924F"}}, "browser": {"show_home_button": "C9B0DAFF1D5EF3CBA224B9D838A5D7F205F20A1F8A8A1C12DAEAAD95A6B5C915"}, "default_search_provider_data": {"template_url_data": "E1C657B3B8D9FE1023632E2577E7056F103BCD72DA33F84EBFB1E9AF12FC4469"}, "enterprise_signin": {"policy_recovery_token": "2FC235DDEFC9DC91E9AA0BD516E924E6D8C2DFF7671D0FDB507159CA51BA01A1"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "B4D8A4BD86A895D6C7CAC59EB9A1EB7AB1A4BF8E356F75006334DFAC09DB2285", "fignfifoniblkonapihmkfakmlgkbkcf": "BE38FD029D6FCB7D864FD4E01D01113FB66988A7AFC40FF1811DC7A6E071B7A8", "kebpgmmmoiggnchlpamiefihdjiaikaf": "0EF2FAB0AED7002740278E19B8281C68DC3FC8648EE1BAF3E5DA2FC51C46D605", "ljglajjnnkapghbckkcmodicjhacbfhk": "8F9F609118146D4E369CE7C4E7E9F651CF1374992F3179A50879C8D8AA90D43E", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "CD7CB3B62AA0522D75D613D3881C077C4F4D27224DE91C98654CF3C7566DE73F", "mnpdbmgpebfihcndnpgdaihnkmloclkd": "8A7D00F5341BE68D809590B2B7D396BA3D736345EEA782C743EF87A5FACF831A", "ncennffkjdiamlpmcbajkmaiiiddgioo": "FEC2EB1E00E35F727F3F3296E894309B35384D43EC17C98933300E2BE60B6E25", "nkeimhogjdpnpccoofpliimaahmaaome": "F6DE7120FF281DD291A442C51CA5EF7A53E5EF35762A9BB4E9D014F4691352E8", "nmmhkkegccagdldgiimedpiccmgmieda": "BE376289BB9110C1BA1E1613B34054F1385ECFE68610C71B15E55EE2ECDE446B"}, "ui": {"developer_mode": "9C0A760353C9777695CF19989B69E43C143FFD788067112410817CB7E0CBC671"}}, "google": {"services": {"account_id": "BB3214790F223D507278D64AC1F722E39E8CC1AFD9538CF33D5001DEF6FBB6F5", "last_signed_in_username": "F941DBEA7C8D5B4B69CA0A44C393FC5B40CA8E39366A64885BEF3FAB2913EA96", "last_username": "7628939FFCE2AD31BCA6250271A12FBB7C43167DB67FA819C819F68B9D8A9FA3"}}, "homepage": "4B7B46AC42E24D6D17ED56C8C73B9CEC4F3C663DB20DF19DDFE50CD86F5FC60F", "homepage_is_newtabpage": "E3E860C02980157A14059299CB6CC14411B5FB3F29CC37CEA99ECCC49C61D6A3", "media": {"cdm": {"origin_data": "0CFD9108FA514D05380BE9734EE07E2C8AA65F787AD57BBBC400D97A7FB566F4"}, "storage_id_salt": "BEA1FFDB2716535C9297455ECACDE2249AC957ABE02C485DCF10AD95F8324094"}, "module_blocklist_cache_md5_digest": "CB0FB5928ED6B030F6A6334652AF8CA3288DB029E5F85445D255E2CFFF0E466A", "pinned_tabs": "1865BF5B3143133049EB3821CE1EC0B8FD81DDDFF811F318EA6515803B4A0CE0", "prefs": {"preference_reset_time": "6CE8E8729573F073B0E933952034BAE61CD10C1250E2F975962D7F2A2F92721A"}, "safebrowsing": {"incidents_sent": "6328D2ABDF12017F51C764411790A472D5283423C7F296863B9BDBD0018C20B9"}, "search_provider_overrides": "024EED9881EA13F5E5D552349E3CC2B8FD139CE043947AE702D15531A285DF9B", "session": {"restore_on_startup": "DD014520F423FE1D70AC956A3CE4AFAB392E2A8F193AB5B03CA8474276B5DC58", "startup_urls": "6A9D211D2460B6A1196AEF9FFD90227C7887B3EDD3C717F615B7ADC4C14EA3FB"}}, "super_mac": "D31394B15F34D779A3B280C889CEA77C17D0BA84B8A75CF018C050256350EFAA"}}