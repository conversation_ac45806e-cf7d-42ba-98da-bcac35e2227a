; This file contains the Google Chrome Portable default settings.
; You can copy this file to GoogleChromePortable\ and change settings in it.
; Any settings missing from that file will have their defaults (listed here)
; used.

[GoogleChromePortable]
Google ChromeDirectory=App\Chrome-bin
; The main app directory
ProfileDirectory=Data\profile
; The profile directory
SettingsDirectory=Data\settings
; The directory containing settings.  Currently only used for the master
; password hash, if enabled.
AdditionalParameters=
; More parameters to pass to chrome.exe, use to enable experimental features.
Google ChromeExecutable=chrome.exe
; The EXE name
WaitForProgram=true
; Waits for the program to run.  Don't set to false as it is required for
; cleanup to avoid leaving files on the local system, and to avoid breaking
; local Google Update and Chrome.
DisableSplashScreen=false
; Disables the splash screen, if it has been compiled into the launcher.
RunLocally=false
; Forces the profile to be copied to the local computer, as if the portable app
; is run from a CD.  The profile will be copied back to the portable device
; afterwards (minus cache files).
CacheInTemp=true
; If this is true, the disk Cache will be set to use the local computer, and
; will be deleted on exit, even if RunLocally is false.

; Note that using either of the previous two options can represent a risk of
; other users of the same computer discovering your internet surfing
; activities on that computer.  Although Google Chrome Portable deletes the
; locally saved data when it is finished, it is possible this may not happen
; (Google Chrome Portable or the computer crashes) and even if it does, some
; of the data may be recoverable using disk recovery tools.  For this reason
; it is recommended you use Incognito mode for any browsing you don't want
; others to know about.  HTTPS browsing is never written to cache and thus not
; an issue.

ImportJava=false
; If true, it will import the Java plugin from a copy of Java Portable if you
; have it installed, and if you do not already have a Java plugin installed in
; Google Chrome Portable.  Note that Java Portable is not yet supported; the
; plugin will attempt to use a locally installed Java.
UsePAMLanguage=true
; Forces Google Chrome to use the language selected by PortableApps.com Menu
; when it is launched from PAM.  The language is only used if Google Chrome
; supports it, otherwise it falls back to the user-selected language (or
; the local system language).  If "true", you won't be able to change the
; language within Google Chrome when launched from PAM since PAM will override
; it.

PortablePasswords=false
; Stores your saved passwords using a master password you select on startup,
; working around Chrome's normal mechanism of tying saved passwords to a local
; user account.  This setting causes a prompt for the master password on
; every startup so it's opt-in.
EncryptPortablePasswords=true
; Uses a master password to re-encrypt passwords.  If this is false, Portable
; Passwords will be insecurely stored as plaintext, but you will not need to
; use a master password.  You may find setting this to false useful if you
; already use an encryption solution such as TrueCrypt to store
; GooglechromePortable.
;
; If you want to change any of the following:
; - The Portable Passwords algorithms, salts, or implementation in such a way
;   that would break your existing Portable Passwords (through an upgrade of
;   Google Chrome Portable that changes these).
; - Your master password, or lack of a master password.
;
; You can keep your Portable Passwords by first ensuring that your passwords
; have been successfully imported into your Google Chrome profile with the
; local user account.  To do this, simply start GCP before upgrading it or
; changing your password or Portable Passwords settings.  Then, delete the
; master password hash in Data\settings and the Portable Passwords files in
; your profile (by default, there is only one in Data\profile\Default.  A
; power user with more than one profile will have one for each profile).
; Next, perform the upgrade, or change the settings, if you wanted to.  Now
; run GCP and enter a new master password (or the same one, but it CAN be new
; if you want).  Once you close Google Chrome the new Portable Passwords
; database will be constructed using your saved passwords from Google Chrome.