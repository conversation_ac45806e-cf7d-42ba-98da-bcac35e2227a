#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 默认图标下载器
管理和下载常用浏览器的默认图标库

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import sys
import json
import threading
import time
from pathlib import Path
from typing import Dict, List, Callable, Optional
import logging

# 导入图标管理器
try:
    from 图标管理器 import icon_manager
    from 配置管理器 import config_manager
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

class DefaultIconDownloader:
    """默认图标下载器 - 管理常用浏览器图标库"""
    
    def __init__(self):
        """初始化默认图标下载器"""
        self.config = config_manager
        self.project_root = self.config.get_project_root()
        self.icons_dir = self.config.get_icons_dir()
        
        # 下载状态
        self.download_progress = {}
        self.download_callbacks = {}
        
        self._setup_logging()
        self._load_icon_definitions()
        
        self.logger.info("默认图标下载器初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.project_root / "日志"
        log_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger("DefaultIconDownloader")
        if not self.logger.handlers:
            handler = logging.FileHandler(log_dir / "默认图标下载器.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _load_icon_definitions(self):
        """加载图标定义"""
        self.default_icons = {
            "chrome": {
                "name": "Google Chrome",
                "description": "Google Chrome浏览器官方图标",
                "urls": [
                    "https://www.google.com/chrome/static/images/chrome-logo.svg",
                    "https://upload.wikimedia.org/wikipedia/commons/a/a5/Google_Chrome_icon_%28September_2014%29.svg",
                    "https://cdn.jsdelivr.net/gh/alrra/browser-logos@master/src/chrome/chrome.png"
                ],
                "fallback_color": "#4285F4",
                "keywords": ["chrome", "谷歌", "google"]
            },
            "firefox": {
                "name": "Mozilla Firefox",
                "description": "Mozilla Firefox浏览器官方图标",
                "urls": [
                    "https://www.mozilla.org/media/img/firefox/favicon.ico",
                    "https://upload.wikimedia.org/wikipedia/commons/a/a0/Firefox_logo%2C_2019.svg",
                    "https://cdn.jsdelivr.net/gh/alrra/browser-logos@master/src/firefox/firefox.png"
                ],
                "fallback_color": "#FF7139",
                "keywords": ["firefox", "火狐", "mozilla"]
            },
            "edge": {
                "name": "Microsoft Edge",
                "description": "Microsoft Edge浏览器官方图标",
                "urls": [
                    "https://upload.wikimedia.org/wikipedia/commons/9/98/Microsoft_Edge_logo_%282019%29.svg",
                    "https://cdn.jsdelivr.net/gh/alrra/browser-logos@master/src/edge/edge.png"
                ],
                "fallback_color": "#0078D4",
                "keywords": ["edge", "微软", "microsoft"]
            },
            "safari": {
                "name": "Safari",
                "description": "Apple Safari浏览器官方图标",
                "urls": [
                    "https://upload.wikimedia.org/wikipedia/commons/5/52/Safari_browser_logo.svg",
                    "https://cdn.jsdelivr.net/gh/alrra/browser-logos@master/src/safari/safari.png"
                ],
                "fallback_color": "#006CFF",
                "keywords": ["safari", "苹果", "apple"]
            },
            "opera": {
                "name": "Opera",
                "description": "Opera浏览器官方图标",
                "urls": [
                    "https://upload.wikimedia.org/wikipedia/commons/4/49/Opera_2015_icon.svg",
                    "https://cdn.jsdelivr.net/gh/alrra/browser-logos@master/src/opera/opera.png"
                ],
                "fallback_color": "#FF1B2D",
                "keywords": ["opera", "欧朋"]
            },
            "brave": {
                "name": "Brave",
                "description": "Brave浏览器官方图标",
                "urls": [
                    "https://upload.wikimedia.org/wikipedia/commons/c/c4/Brave_lion.svg",
                    "https://cdn.jsdelivr.net/gh/alrra/browser-logos@master/src/brave/brave.png"
                ],
                "fallback_color": "#FB542B",
                "keywords": ["brave", "勇敢"]
            },
            "vivaldi": {
                "name": "Vivaldi",
                "description": "Vivaldi浏览器官方图标",
                "urls": [
                    "https://upload.wikimedia.org/wikipedia/commons/e/e4/Vivaldi_web_browser_logo.svg",
                    "https://cdn.jsdelivr.net/gh/alrra/browser-logos@master/src/vivaldi/vivaldi.png"
                ],
                "fallback_color": "#EF3939",
                "keywords": ["vivaldi", "韦瓦第"]
            },
            "generic": {
                "name": "通用浏览器",
                "description": "通用浏览器图标",
                "urls": [],
                "fallback_color": "#6C757D",
                "keywords": ["generic", "通用", "默认"]
            }
        }
    
    def get_available_default_icons(self) -> Dict[str, Dict]:
        """获取可用的默认图标列表"""
        return self.default_icons.copy()
    
    def search_icons(self, keyword: str) -> List[str]:
        """
        根据关键词搜索图标
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的图标ID列表
        """
        keyword = keyword.lower()
        matches = []
        
        for icon_id, icon_info in self.default_icons.items():
            # 检查名称
            if keyword in icon_info["name"].lower():
                matches.append(icon_id)
                continue
            
            # 检查关键词
            for kw in icon_info["keywords"]:
                if keyword in kw.lower():
                    matches.append(icon_id)
                    break
        
        return matches
    
    def download_icon(self, icon_id: str, progress_callback: Optional[Callable] = None) -> bool:
        """
        下载指定图标
        
        Args:
            icon_id: 图标ID
            progress_callback: 进度回调函数 (success: bool, message: str)
            
        Returns:
            是否下载成功
        """
        if icon_id not in self.default_icons:
            self.logger.error(f"未知图标ID: {icon_id}")
            if progress_callback:
                progress_callback(False, f"未知图标ID: {icon_id}")
            return False
        
        icon_info = self.default_icons[icon_id]
        
        # 检查是否已存在
        existing_icon = self._check_existing_icon(icon_id)
        if existing_icon:
            self.logger.info(f"图标已存在: {icon_id}")
            if progress_callback:
                progress_callback(True, f"图标已存在: {icon_info['name']}")
            return True
        
        # 尝试从URL下载
        for i, url in enumerate(icon_info["urls"]):
            try:
                self.logger.info(f"尝试下载 {icon_id} 从 URL {i+1}/{len(icon_info['urls'])}")
                if progress_callback:
                    progress_callback(None, f"尝试下载 {icon_info['name']} ({i+1}/{len(icon_info['urls'])})")
                
                def download_callback(success, path, message):
                    if success:
                        self.logger.info(f"图标下载成功: {icon_id}")
                        if progress_callback:
                            progress_callback(True, f"{icon_info['name']} 下载成功")
                    else:
                        self.logger.warning(f"URL下载失败: {url} - {message}")
                
                result_path = icon_manager.download_icon_from_url(url, icon_id, download_callback)
                if result_path:
                    return True
                    
            except Exception as e:
                self.logger.warning(f"下载失败: {url} - {e}")
                continue
        
        # 所有URL都失败，创建默认图标
        self.logger.info(f"URL下载失败，创建默认图标: {icon_id}")
        if progress_callback:
            progress_callback(None, f"创建默认图标: {icon_info['name']}")
        
        fallback_path = icon_manager.create_default_icon(icon_id, icon_info["fallback_color"])
        if fallback_path:
            self.logger.info(f"默认图标创建成功: {icon_id}")
            if progress_callback:
                progress_callback(True, f"{icon_info['name']} 默认图标创建成功")
            return True
        else:
            self.logger.error(f"默认图标创建失败: {icon_id}")
            if progress_callback:
                progress_callback(False, f"{icon_info['name']} 创建失败")
            return False
    
    def _check_existing_icon(self, icon_id: str) -> Optional[str]:
        """检查图标是否已存在"""
        icon_path = self.icons_dir / f"{icon_id}.png"
        if icon_path.exists():
            return str(icon_path)
        
        # 检查其他格式
        for ext in ['.ico', '.jpg', '.jpeg']:
            alt_path = self.icons_dir / f"{icon_id}{ext}"
            if alt_path.exists():
                return str(alt_path)
        
        return None
    
    def download_all_icons(self, progress_callback: Optional[Callable] = None) -> Dict[str, bool]:
        """
        下载所有默认图标
        
        Args:
            progress_callback: 进度回调函数 (icon_id: str, success: bool, message: str)
            
        Returns:
            下载结果字典
        """
        results = {}
        total = len(self.default_icons)
        
        for i, icon_id in enumerate(self.default_icons.keys(), 1):
            self.logger.info(f"下载进度: {i}/{total} - {icon_id}")
            
            def icon_progress(success, message):
                if progress_callback:
                    progress_callback(icon_id, success, message, i, total)
            
            results[icon_id] = self.download_icon(icon_id, icon_progress)
            
            # 短暂延迟，避免请求过快
            time.sleep(0.5)
        
        return results
    
    def download_icons_async(self, icon_ids: List[str], progress_callback: Optional[Callable] = None):
        """异步下载多个图标"""
        def download_worker():
            results = {}
            total = len(icon_ids)
            
            for i, icon_id in enumerate(icon_ids, 1):
                def icon_progress(success, message):
                    if progress_callback:
                        progress_callback(icon_id, success, message, i, total)
                
                results[icon_id] = self.download_icon(icon_id, icon_progress)
                time.sleep(0.5)  # 延迟
            
            if progress_callback:
                progress_callback("完成", True, f"批量下载完成", total, total)
        
        thread = threading.Thread(target=download_worker, daemon=True)
        thread.start()
    
    def get_download_status(self) -> Dict[str, Dict]:
        """获取下载状态"""
        status = {}
        
        for icon_id, icon_info in self.default_icons.items():
            existing_path = self._check_existing_icon(icon_id)
            status[icon_id] = {
                "name": icon_info["name"],
                "exists": existing_path is not None,
                "path": existing_path,
                "description": icon_info["description"]
            }
        
        return status
    
    def update_icon_definitions(self, new_definitions: Dict[str, Dict]):
        """更新图标定义"""
        try:
            self.default_icons.update(new_definitions)
            
            # 保存到文件
            definitions_file = self.icons_dir / "icon_definitions.json"
            with open(definitions_file, 'w', encoding='utf-8') as f:
                json.dump(self.default_icons, f, ensure_ascii=False, indent=2)
            
            self.logger.info("图标定义更新成功")
            return True
            
        except Exception as e:
            self.logger.error(f"更新图标定义失败: {e}")
            return False

# 全局默认图标下载器实例
default_icon_downloader = DefaultIconDownloader()

def get_default_icons() -> Dict[str, Dict]:
    """快捷函数：获取默认图标列表"""
    return default_icon_downloader.get_available_default_icons()

def download_default_icon(icon_id: str, callback=None) -> bool:
    """快捷函数：下载默认图标"""
    return default_icon_downloader.download_icon(icon_id, callback)

def search_default_icons(keyword: str) -> List[str]:
    """快捷函数：搜索默认图标"""
    return default_icon_downloader.search_icons(keyword)

if __name__ == "__main__":
    # 测试默认图标下载器
    print("📥 默认图标下载器测试")
    
    # 获取可用图标
    icons = get_default_icons()
    print(f"可用默认图标: {len(icons)}个")
    for icon_id, icon_info in list(icons.items())[:3]:
        print(f"  - {icon_id}: {icon_info['name']}")
    
    # 测试搜索
    search_results = search_default_icons("chrome")
    print(f"搜索'chrome': {search_results}")
    
    # 获取下载状态
    status = default_icon_downloader.get_download_status()
    existing_count = sum(1 for s in status.values() if s['exists'])
    print(f"已存在图标: {existing_count}/{len(status)}个")
    
    print("✅ 默认图标下载器测试完成")
