#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终集成测试
验证整个系统的集成和工作流程

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import sys
import subprocess
import time
from pathlib import Path

def test_startup_manager():
    """测试启动管理器"""
    print("🚀 测试启动管理器")
    
    try:
        # 测试启动管理器的环境检查功能
        result = subprocess.run([
            sys.executable, "启动管理器.py"
        ], capture_output=True, text=True, timeout=10, input="\n")
        
        output = result.stdout
        
        # 检查关键输出
        checks = [
            ("Python版本检查", "✅ Python版本"),
            ("操作系统检查", "✅ 操作系统"),
            ("项目结构检查", "✅ GoogleChromePortable"),
            ("依赖检查", "✅ 所有检查通过"),
            ("启动浏览器管理器", "🚀 正在启动浏览器管理器")
        ]
        
        for check_name, expected_text in checks:
            if expected_text in output:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name} - 未找到: {expected_text}")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("✅ 启动管理器正常启动（超时表示GUI已启动）")
        return True
    except Exception as e:
        print(f"❌ 启动管理器测试失败: {e}")
        return False

def test_command_line_interface():
    """测试命令行界面"""
    print("\n⌨️ 测试命令行界面")
    
    try:
        # 测试命令行版本的浏览器管理器
        result = subprocess.run([
            sys.executable, "浏览器管理器.py"
        ], capture_output=True, text=True, timeout=5, input="5\n0\n")
        
        output = result.stdout
        
        # 检查关键输出
        if "浏览器管理器 v2.2.1" in output:
            print("✅ 命令行界面标题正确")
        else:
            print("❌ 命令行界面标题错误")
        
        if "📋 列出所有浏览器" in output:
            print("✅ 菜单显示正常")
        else:
            print("❌ 菜单显示异常")
        
        if "感谢使用浏览器管理器" in output:
            print("✅ 正常退出")
        else:
            print("❌ 退出异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 命令行界面测试失败: {e}")
        return False

def test_module_imports():
    """测试模块导入"""
    print("\n📦 测试模块导入")
    
    modules = [
        ("配置管理器", "from 配置管理器 import config_manager"),
        ("主题管理器", "from 主题管理器 import theme_manager"),
        ("浏览器管理器", "from 浏览器管理器 import BrowserManager"),
        ("快捷方式管理器", "from 快捷方式管理器 import shortcut_manager"),
        ("GUI管理器", "from 浏览器管理器GUI import BrowserManagerGUI")
    ]
    
    for module_name, import_statement in modules:
        try:
            exec(import_statement)
            print(f"✅ {module_name}导入成功")
        except Exception as e:
            print(f"❌ {module_name}导入失败: {e}")
            return False
    
    return True

def test_configuration_system():
    """测试配置系统"""
    print("\n⚙️ 测试配置系统")
    
    try:
        from 配置管理器 import get_config, set_config
        from 主题管理器 import switch_theme
        
        # 测试配置读写
        test_value = "integration_test_value"
        if set_config("test.integration", test_value):
            read_value = get_config("test.integration")
            if read_value == test_value:
                print("✅ 配置读写正常")
            else:
                print("❌ 配置读写异常")
                return False
        else:
            print("❌ 配置写入失败")
            return False
        
        # 测试主题切换
        original_theme = get_config('system_settings.theme')
        target_theme = "dark_theme" if original_theme == "modern_blue" else "modern_blue"
        
        if switch_theme(target_theme):
            if switch_theme(original_theme):
                print("✅ 主题切换正常")
            else:
                print("❌ 主题恢复失败")
                return False
        else:
            print("❌ 主题切换失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        return False

def test_browser_operations():
    """测试浏览器操作"""
    print("\n🖥️ 测试浏览器操作")
    
    try:
        from 浏览器管理器 import BrowserManager
        from 快捷方式管理器 import create_desktop_shortcut
        
        manager = BrowserManager()
        
        # 测试浏览器列表
        browsers = manager.list_browsers()
        print(f"✅ 浏览器列表获取成功: {len(browsers)}个浏览器")
        
        if browsers:
            # 测试浏览器健康检查
            test_browser = browsers[0]
            browser_dir = Path(test_browser['path'])
            is_healthy = manager._check_browser_health(browser_dir)
            
            if is_healthy:
                print(f"✅ 浏览器健康检查正常: {test_browser['name']}")
            else:
                print(f"❌ 浏览器健康检查异常: {test_browser['name']}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器操作测试失败: {e}")
        return False

def test_file_integrity():
    """测试文件完整性"""
    print("\n📁 测试文件完整性")
    
    try:
        from 配置管理器 import config_manager
        
        project_root = config_manager.get_project_root()
        
        # 检查关键文件
        critical_files = [
            "browsers.cognigraph.json",
            "项目配置.json",
            "README.md"
        ]
        
        for file_name in critical_files:
            file_path = project_root / file_name
            if file_path.exists() and file_path.stat().st_size > 0:
                print(f"✅ 关键文件正常: {file_name}")
            else:
                print(f"❌ 关键文件异常: {file_name}")
                return False
        
        # 检查日志文件
        log_dir = project_root / "日志"
        if log_dir.exists():
            log_files = list(log_dir.glob("*.log"))
            if log_files:
                print(f"✅ 日志系统正常: {len(log_files)}个日志文件")
            else:
                print("❌ 日志系统异常: 无日志文件")
                return False
        else:
            print("❌ 日志目录不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件完整性测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    print("\n⚡ 测试性能")
    
    try:
        from 浏览器管理器 import BrowserManager
        from 主题管理器 import theme_manager
        
        # 测试浏览器列表性能
        start_time = time.time()
        manager = BrowserManager()
        browsers = manager.list_browsers()
        list_time = time.time() - start_time
        
        if list_time < 2.0:
            print(f"✅ 浏览器列表性能良好: {list_time:.3f}秒")
        else:
            print(f"⚠️ 浏览器列表性能较慢: {list_time:.3f}秒")
        
        # 测试主题切换性能
        start_time = time.time()
        original_theme = theme_manager.get_current_theme()
        target_theme = "dark_theme" if original_theme == "modern_blue" else "modern_blue"
        theme_manager.switch_theme(target_theme)
        theme_manager.switch_theme(original_theme)
        theme_time = time.time() - start_time
        
        if theme_time < 1.0:
            print(f"✅ 主题切换性能良好: {theme_time:.3f}秒")
        else:
            print(f"⚠️ 主题切换性能较慢: {theme_time:.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 最终集成测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_module_imports),
        ("配置系统", test_configuration_system),
        ("浏览器操作", test_browser_operations),
        ("文件完整性", test_file_integrity),
        ("性能测试", test_performance),
        ("命令行界面", test_command_line_interface),
        ("启动管理器", test_startup_manager)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 最终测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有集成测试通过！系统完全正常！")
        print("\n🚀 系统已准备就绪，可以正式使用！")
        print("💡 使用方法: python 启动管理器.py")
        return True
    else:
        print("⚠️ 部分测试未通过，系统可能存在问题")
        return False

if __name__ == "__main__":
    main()
