#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 配置管理器
统一管理系统所有配置，包括项目配置、主题设置、语言偏好等

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging

class ConfigManager:
    """统一配置管理器 - 项目的配置中枢"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.project_root = Path(__file__).parent
        self.config_file = self.project_root / "项目配置.json"
        self.cognigraph_file = self.project_root / "browsers.cognigraph.json"
        self._config_cache = {}
        self._setup_logging()
        self._ensure_config_exists()
    
    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.project_root / "日志"
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "配置管理器.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger("ConfigManager")
    
    def _ensure_config_exists(self):
        """确保配置文件存在，不存在则创建默认配置"""
        if not self.config_file.exists():
            self._create_default_config()
        
        # 加载配置到缓存
        self._load_config()
    
    def _create_default_config(self):
        """创建默认项目配置文件"""
        default_config = {
            "project_info": {
                "name": "浏览器多账号绿色版",
                "version": "v2.2.1",
                "author": "CogniGraph™ 认知图迹系统",
                "created_date": "2025-07-25",
                "last_updated": "2025-07-25"
            },
            "system_settings": {
                "language": "zh_CN",  # zh_CN, en_US
                "theme": "modern_blue",  # modern_blue, dark_theme
                "auto_update": True,
                "check_update_on_startup": True,
                "log_level": "INFO"
            },
            "browser_settings": {
                "default_chrome_path": "./GoogleChromePortable/GoogleChromePortable.exe",
                "browsers_dir": "./浏览器实例",
                "icons_dir": "./默认图标",
                "backup_dir": "./备份"
            },
            "icon_settings": {
                "default_icon_size": 64,
                "supported_formats": [".ico", ".png", ".jpg", ".jpeg", ".svg", ".bmp"],
                "download_timeout": 30,
                "max_download_retries": 3
            },
            "plugin_sync": {
                "backup_before_sync": True,
                "sync_timeout": 60,
                "excluded_extensions": []
            },
            "gui_settings": {
                "window_width": 800,
                "window_height": 600,
                "window_resizable": True,
                "show_tooltips": True,
                "animation_enabled": True
            },
            "feature_flags": {
                "enable_icon_download": True,
                "enable_plugin_sync": True,
                "enable_theme_switch": True,
                "enable_language_switch": True,
                "enable_auto_update": True,
                "enable_desktop_shortcuts": True
            }
        }
        
        self._save_config(default_config)
        self.logger.info("创建默认配置文件成功")
    
    def _load_config(self):
        """加载配置文件到缓存"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self._config_cache = json.load(f)
            self.logger.info("配置文件加载成功")
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            self._create_default_config()
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            self._config_cache = config
            self.logger.info("配置文件保存成功")
        except Exception as e:
            self.logger.error(f"配置文件保存失败: {e}")
            raise
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 'system_settings.language'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key_path.split('.')
            value = self._config_cache
            
            for key in keys:
                value = value[key]
            
            return value
        except (KeyError, TypeError):
            self.logger.warning(f"配置键 {key_path} 不存在，返回默认值: {default}")
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
            
        Returns:
            是否设置成功
        """
        try:
            keys = key_path.split('.')
            config = self._config_cache.copy()
            current = config
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # 设置值
            current[keys[-1]] = value
            
            # 保存配置
            self._save_config(config)
            self.logger.info(f"配置 {key_path} 设置为: {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置配置 {key_path} 失败: {e}")
            return False
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config_cache.copy()
    
    def reload(self):
        """重新加载配置文件"""
        self._load_config()
        self.logger.info("配置文件重新加载完成")
    
    def reset_to_default(self):
        """重置为默认配置"""
        self._create_default_config()
        self.logger.info("配置已重置为默认值")
    
    def get_project_root(self) -> Path:
        """获取项目根目录"""
        return self.project_root
    
    def get_browsers_dir(self) -> Path:
        """获取浏览器实例目录"""
        browsers_dir = self.get('browser_settings.browsers_dir', './浏览器实例')
        return self.project_root / browsers_dir
    
    def get_icons_dir(self) -> Path:
        """获取图标目录"""
        icons_dir = self.get('icon_settings.icons_dir', './默认图标')
        return self.project_root / icons_dir
    
    def get_chrome_path(self) -> Path:
        """获取Chrome Portable路径"""
        chrome_path = self.get('browser_settings.default_chrome_path', 
                              './GoogleChromePortable/GoogleChromePortable.exe')
        return self.project_root / chrome_path

# 全局配置管理器实例
config_manager = ConfigManager()

def get_config(key_path: str, default: Any = None) -> Any:
    """快捷函数：获取配置值"""
    return config_manager.get(key_path, default)

def set_config(key_path: str, value: Any) -> bool:
    """快捷函数：设置配置值"""
    return config_manager.set(key_path, value)

if __name__ == "__main__":
    # 测试配置管理器
    print("🔧 配置管理器测试")
    print(f"项目根目录: {config_manager.get_project_root()}")
    print(f"当前语言: {get_config('system_settings.language')}")
    print(f"当前主题: {get_config('system_settings.theme')}")
    print(f"Chrome路径: {config_manager.get_chrome_path()}")
    print("✅ 配置管理器测试完成")
