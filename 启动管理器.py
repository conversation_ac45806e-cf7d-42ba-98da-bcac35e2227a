#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 启动管理器
一键启动器，自动检查环境，智能选择界面

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25

使用方法：
    python 启动管理器.py
"""

import sys
import os
import subprocess
import platform
from pathlib import Path
import importlib.util

def print_banner():
    """显示项目横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                🌟 浏览器多账号绿色版 v2.2.1                    ║
║                                                              ║
║  🚀 一键启动 | 🎨 多主题 | 🌍 多语言 | 🔄 自动更新            ║
║  📁 真正便携 | 🔒 账号隔离 | 🔄 插件同步 | 🎯 专业级体验      ║
║                                                              ║
║  基于 CogniGraph™ 认知图迹系统 v0.002                        ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python环境...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.7或更高版本")
        print("   请访问 https://python.org 下载最新版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_platform():
    """检查操作系统平台"""
    print("🔍 检查操作系统...")
    
    system = platform.system()
    if system != "Windows":
        print(f"⚠️  当前系统: {system}")
        print("   本项目主要为Windows设计，其他平台可能存在兼容性问题")
        return False
    
    print(f"✅ 操作系统: {system} {platform.release()}")
    return True

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"📦 安装 {package_name}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package_name} 安装失败")
        return False

def check_dependencies():
    """检查和安装依赖"""
    print("🔍 检查Python依赖...")
    
    # 读取requirements.txt
    requirements_file = Path(__file__).parent / "requirements.txt"
    if not requirements_file.exists():
        print("⚠️  requirements.txt 文件不存在")
        return True
    
    # 核心依赖列表
    core_dependencies = [
        ("pywin32", "pywin32>=306"),
        ("requests", "requests>=2.31.0"),
        ("PIL", "Pillow>=10.0.0")
    ]
    
    missing_packages = []
    
    for import_name, package_name in core_dependencies:
        try:
            if import_name == "PIL":
                import PIL
            else:
                __import__(import_name)
            print(f"✅ {import_name} 已安装")
        except ImportError:
            print(f"❌ {import_name} 未安装")
            missing_packages.append(package_name)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n📦 需要安装 {len(missing_packages)} 个依赖包...")
        for package in missing_packages:
            if not install_package(package):
                print(f"❌ 依赖安装失败，请手动运行: pip install {package}")
                return False
        print("✅ 所有依赖安装完成")
    
    return True

def check_project_structure():
    """检查项目结构"""
    print("🔍 检查项目结构...")
    
    project_root = Path(__file__).parent
    required_items = [
        ("GoogleChromePortable", "目录"),
        ("默认图标", "目录"),
        ("README.md", "文件"),
        ("browsers.cognigraph.json", "文件"),
        ("配置管理器.py", "文件")
    ]
    
    missing_items = []
    for item_name, item_type in required_items:
        item_path = project_root / item_name
        if item_type == "目录" and not item_path.is_dir():
            missing_items.append(f"{item_name} (目录)")
        elif item_type == "文件" and not item_path.is_file():
            missing_items.append(f"{item_name} (文件)")
        else:
            print(f"✅ {item_name}")
    
    if missing_items:
        print("❌ 缺失的项目文件:")
        for item in missing_items:
            print(f"   - {item}")
        return False
    
    return True

def launch_gui():
    """启动图形界面"""
    try:
        print("🚀 启动图形界面...")
        
        # 检查GUI文件是否存在
        gui_file = Path(__file__).parent / "浏览器管理器GUI.py"
        if not gui_file.exists():
            print("❌ 图形界面文件不存在，启动命令行版本...")
            return launch_cli()
        
        # 导入并启动GUI
        spec = importlib.util.spec_from_file_location("browser_gui", gui_file)
        browser_gui = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(browser_gui)
        
        # 启动主界面
        if hasattr(browser_gui, 'main'):
            browser_gui.main()
        else:
            print("❌ GUI模块缺少main函数")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 图形界面启动失败: {e}")
        print("🔄 尝试启动命令行版本...")
        return launch_cli()

def launch_cli():
    """启动命令行界面"""
    try:
        print("⌨️  启动命令行界面...")
        
        # 检查CLI文件是否存在
        cli_file = Path(__file__).parent / "浏览器管理器.py"
        if not cli_file.exists():
            print("❌ 命令行界面文件不存在")
            print("💡 请检查项目文件完整性")
            return False
        
        # 导入并启动CLI
        spec = importlib.util.spec_from_file_location("browser_cli", cli_file)
        browser_cli = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(browser_cli)
        
        # 启动主界面
        if hasattr(browser_cli, 'main'):
            browser_cli.main()
        else:
            print("❌ CLI模块缺少main函数")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 命令行界面启动失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 环境检查
    checks = [
        ("Python版本", check_python_version),
        ("操作系统", check_platform),
        ("项目结构", check_project_structure),
        ("Python依赖", check_dependencies)
    ]
    
    print("🔍 开始环境检查...\n")
    
    for check_name, check_func in checks:
        if not check_func():
            print(f"\n❌ {check_name}检查失败")
            print("🛠️  请解决上述问题后重新运行")
            input("\n按回车键退出...")
            sys.exit(1)
        print()
    
    print("✅ 所有检查通过！\n")
    
    # 智能启动
    print("🚀 正在启动浏览器管理器...")
    
    # 优先尝试图形界面
    if not launch_gui():
        print("\n❌ 启动失败")
        print("💡 请检查错误信息并重试")
        input("\n按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 启动器发生未知错误: {e}")
        print("💡 请检查Python环境和项目文件")
        input("\n按回车键退出...")
        sys.exit(1)
