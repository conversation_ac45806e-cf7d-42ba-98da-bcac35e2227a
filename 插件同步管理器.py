#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v2.2.1 - 插件同步管理器
实现浏览器间插件的智能同步，支持插件矩阵分析和选择性同步

作者：CogniGraph™ 认知图迹系统
版本：v2.2.1
更新：2025-07-25
"""

import os
import sys
import json
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
import time
from datetime import datetime

# 导入配置管理器
try:
    from 配置管理器 import config_manager, get_config
    from 浏览器管理器 import BrowserManager
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

class PluginSyncManager:
    """插件同步管理器 - 智能插件同步和管理"""
    
    def __init__(self):
        """初始化插件同步管理器"""
        self.config = config_manager
        self.browser_manager = BrowserManager()
        self.project_root = self.config.get_project_root()
        self.browsers_dir = self.config.get_browsers_dir()
        self.backup_dir = self.project_root / "备份" / "插件备份"
        
        # 确保备份目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 插件数据缓存
        self.plugin_cache = {}
        self.sync_history = []
        
        self._setup_logging()
        self._load_sync_history()
        
        self.logger.info("插件同步管理器初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.project_root / "日志"
        log_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger("PluginSyncManager")
        if not self.logger.handlers:
            handler = logging.FileHandler(log_dir / "插件同步管理器.log", encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _load_sync_history(self):
        """加载同步历史"""
        try:
            history_file = self.backup_dir / "sync_history.json"
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.sync_history = json.load(f)
                self.logger.info(f"同步历史加载成功: {len(self.sync_history)}条记录")
            else:
                self.sync_history = []
                self.logger.info("创建新的同步历史")
        except Exception as e:
            self.logger.error(f"加载同步历史失败: {e}")
            self.sync_history = []
    
    def _save_sync_history(self):
        """保存同步历史"""
        try:
            history_file = self.backup_dir / "sync_history.json"
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.sync_history, f, ensure_ascii=False, indent=2)
            self.logger.info("同步历史保存成功")
        except Exception as e:
            self.logger.error(f"保存同步历史失败: {e}")
    
    def scan_browser_plugins(self, browser_name: str) -> Dict[str, Dict]:
        """
        扫描浏览器插件
        
        Args:
            browser_name: 浏览器名称
            
        Returns:
            插件信息字典
        """
        try:
            browser_dir = self.browsers_dir / browser_name
            data_dir = browser_dir / f"Data_{browser_name}"
            
            if not data_dir.exists():
                self.logger.warning(f"浏览器数据目录不存在: {browser_name}")
                return {}
            
            plugins = {}
            
            # Chrome扩展目录
            extensions_dir = data_dir / "Default" / "Extensions"
            if extensions_dir.exists():
                plugins.update(self._scan_chrome_extensions(extensions_dir))
            
            # 用户脚本目录
            userscripts_dir = data_dir / "Default" / "User Scripts"
            if userscripts_dir.exists():
                plugins.update(self._scan_user_scripts(userscripts_dir))
            
            self.logger.info(f"浏览器 {browser_name} 扫描完成: {len(plugins)}个插件")
            return plugins
            
        except Exception as e:
            self.logger.error(f"扫描浏览器插件失败: {browser_name} - {e}")
            return {}
    
    def _scan_chrome_extensions(self, extensions_dir: Path) -> Dict[str, Dict]:
        """扫描Chrome扩展"""
        extensions = {}
        
        try:
            for ext_dir in extensions_dir.iterdir():
                if ext_dir.is_dir():
                    ext_id = ext_dir.name
                    
                    # 查找最新版本
                    latest_version = None
                    latest_version_dir = None
                    
                    for version_dir in ext_dir.iterdir():
                        if version_dir.is_dir():
                            if latest_version is None or version_dir.name > latest_version:
                                latest_version = version_dir.name
                                latest_version_dir = version_dir
                    
                    if latest_version_dir:
                        manifest_file = latest_version_dir / "manifest.json"
                        if manifest_file.exists():
                            try:
                                with open(manifest_file, 'r', encoding='utf-8') as f:
                                    manifest = json.load(f)
                                
                                extensions[ext_id] = {
                                    "type": "extension",
                                    "name": manifest.get("name", ext_id),
                                    "version": manifest.get("version", latest_version),
                                    "description": manifest.get("description", ""),
                                    "path": str(latest_version_dir),
                                    "manifest": manifest,
                                    "size": self._get_dir_size(latest_version_dir)
                                }
                            except Exception as e:
                                self.logger.warning(f"读取扩展manifest失败: {ext_id} - {e}")
                                extensions[ext_id] = {
                                    "type": "extension",
                                    "name": ext_id,
                                    "version": latest_version,
                                    "description": "无法读取详细信息",
                                    "path": str(latest_version_dir),
                                    "manifest": {},
                                    "size": self._get_dir_size(latest_version_dir)
                                }
        except Exception as e:
            self.logger.error(f"扫描Chrome扩展失败: {e}")
        
        return extensions
    
    def _scan_user_scripts(self, userscripts_dir: Path) -> Dict[str, Dict]:
        """扫描用户脚本"""
        scripts = {}
        
        try:
            for script_file in userscripts_dir.glob("*.js"):
                script_id = script_file.stem
                
                # 读取脚本头部信息
                script_info = self._parse_userscript_header(script_file)
                
                scripts[f"userscript_{script_id}"] = {
                    "type": "userscript",
                    "name": script_info.get("name", script_id),
                    "version": script_info.get("version", "1.0"),
                    "description": script_info.get("description", ""),
                    "path": str(script_file),
                    "metadata": script_info,
                    "size": script_file.stat().st_size
                }
        except Exception as e:
            self.logger.error(f"扫描用户脚本失败: {e}")
        
        return scripts
    
    def _parse_userscript_header(self, script_file: Path) -> Dict[str, str]:
        """解析用户脚本头部信息"""
        metadata = {}
        
        try:
            with open(script_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            in_header = False
            for line in lines:
                line = line.strip()
                
                if line.startswith("// ==UserScript=="):
                    in_header = True
                    continue
                elif line.startswith("// ==/UserScript=="):
                    break
                elif in_header and line.startswith("// @"):
                    parts = line[3:].split(None, 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        metadata[key] = value
        except Exception as e:
            self.logger.warning(f"解析用户脚本头部失败: {script_file} - {e}")
        
        return metadata
    
    def _get_dir_size(self, directory: Path) -> int:
        """获取目录大小"""
        try:
            total_size = 0
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            return total_size
        except Exception:
            return 0
    
    def generate_plugin_matrix(self) -> Dict[str, Dict]:
        """
        生成插件矩阵
        
        Returns:
            插件矩阵数据
        """
        try:
            browsers = self.browser_manager.list_browsers()
            matrix = {
                "browsers": [],
                "plugins": {},
                "matrix": {},
                "statistics": {}
            }
            
            # 扫描所有浏览器
            all_plugins = {}
            browser_plugins = {}
            
            for browser in browsers:
                browser_name = browser['name']
                matrix["browsers"].append(browser_name)
                
                plugins = self.scan_browser_plugins(browser_name)
                browser_plugins[browser_name] = plugins
                
                # 收集所有插件
                for plugin_id, plugin_info in plugins.items():
                    if plugin_id not in all_plugins:
                        all_plugins[plugin_id] = plugin_info.copy()
                        all_plugins[plugin_id]["browsers"] = []
                    
                    all_plugins[plugin_id]["browsers"].append(browser_name)
            
            # 构建矩阵
            matrix["plugins"] = all_plugins
            
            for plugin_id in all_plugins:
                matrix["matrix"][plugin_id] = {}
                for browser_name in matrix["browsers"]:
                    matrix["matrix"][plugin_id][browser_name] = plugin_id in browser_plugins.get(browser_name, {})
            
            # 统计信息
            matrix["statistics"] = {
                "total_browsers": len(matrix["browsers"]),
                "total_plugins": len(all_plugins),
                "common_plugins": len([p for p in all_plugins.values() if len(p["browsers"]) == len(matrix["browsers"])]),
                "unique_plugins": len([p for p in all_plugins.values() if len(p["browsers"]) == 1])
            }
            
            self.logger.info(f"插件矩阵生成完成: {matrix['statistics']}")
            return matrix
            
        except Exception as e:
            self.logger.error(f"生成插件矩阵失败: {e}")
            return {}
    
    def sync_plugins(self, source_browser: str, target_browsers: List[str], 
                    plugin_ids: Optional[List[str]] = None, 
                    backup_before_sync: bool = True) -> Dict[str, bool]:
        """
        同步插件
        
        Args:
            source_browser: 源浏览器
            target_browsers: 目标浏览器列表
            plugin_ids: 要同步的插件ID列表，None表示全部
            backup_before_sync: 同步前是否备份
            
        Returns:
            同步结果
        """
        try:
            results = {}
            
            # 获取源浏览器插件
            source_plugins = self.scan_browser_plugins(source_browser)
            if not source_plugins:
                self.logger.error(f"源浏览器无插件: {source_browser}")
                return {browser: False for browser in target_browsers}
            
            # 确定要同步的插件
            if plugin_ids is None:
                sync_plugins = source_plugins
            else:
                sync_plugins = {pid: source_plugins[pid] for pid in plugin_ids if pid in source_plugins}
            
            self.logger.info(f"开始同步 {len(sync_plugins)} 个插件从 {source_browser} 到 {len(target_browsers)} 个浏览器")
            
            # 为每个目标浏览器同步
            for target_browser in target_browsers:
                try:
                    if backup_before_sync:
                        self._backup_browser_plugins(target_browser)
                    
                    success = self._sync_to_browser(source_browser, target_browser, sync_plugins)
                    results[target_browser] = success
                    
                    if success:
                        self.logger.info(f"同步成功: {source_browser} -> {target_browser}")
                    else:
                        self.logger.error(f"同步失败: {source_browser} -> {target_browser}")
                        
                except Exception as e:
                    self.logger.error(f"同步到 {target_browser} 失败: {e}")
                    results[target_browser] = False
            
            # 记录同步历史
            self._record_sync_history(source_browser, target_browsers, list(sync_plugins.keys()), results)
            
            return results
            
        except Exception as e:
            self.logger.error(f"插件同步失败: {e}")
            return {browser: False for browser in target_browsers}
    
    def _backup_browser_plugins(self, browser_name: str) -> bool:
        """备份浏览器插件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.backup_dir / f"{browser_name}_{timestamp}"
            
            browser_dir = self.browsers_dir / browser_name
            data_dir = browser_dir / f"Data_{browser_name}"
            extensions_dir = data_dir / "Default" / "Extensions"
            
            if extensions_dir.exists():
                shutil.copytree(extensions_dir, backup_path / "Extensions")
                self.logger.info(f"插件备份成功: {browser_name} -> {backup_path}")
                return True
            else:
                self.logger.warning(f"扩展目录不存在，跳过备份: {browser_name}")
                return True
                
        except Exception as e:
            self.logger.error(f"备份插件失败: {browser_name} - {e}")
            return False
    
    def _sync_to_browser(self, source_browser: str, target_browser: str, plugins: Dict[str, Dict]) -> bool:
        """同步插件到目标浏览器"""
        try:
            source_data_dir = self.browsers_dir / source_browser / f"Data_{source_browser}"
            target_data_dir = self.browsers_dir / target_browser / f"Data_{target_browser}"
            
            if not target_data_dir.exists():
                target_data_dir.mkdir(parents=True)
            
            # 确保目标扩展目录存在
            target_extensions_dir = target_data_dir / "Default" / "Extensions"
            target_extensions_dir.mkdir(parents=True, exist_ok=True)
            
            success_count = 0
            
            for plugin_id, plugin_info in plugins.items():
                try:
                    if plugin_info["type"] == "extension":
                        source_path = Path(plugin_info["path"]).parent
                        target_path = target_extensions_dir / plugin_id
                        
                        if target_path.exists():
                            shutil.rmtree(target_path)
                        
                        shutil.copytree(source_path, target_path)
                        success_count += 1
                        
                    elif plugin_info["type"] == "userscript":
                        source_path = Path(plugin_info["path"])
                        target_scripts_dir = target_data_dir / "Default" / "User Scripts"
                        target_scripts_dir.mkdir(parents=True, exist_ok=True)
                        
                        target_path = target_scripts_dir / source_path.name
                        shutil.copy2(source_path, target_path)
                        success_count += 1
                        
                except Exception as e:
                    self.logger.warning(f"同步插件失败: {plugin_id} - {e}")
                    continue
            
            self.logger.info(f"同步完成: {success_count}/{len(plugins)} 个插件成功")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"同步到浏览器失败: {target_browser} - {e}")
            return False
    
    def _record_sync_history(self, source: str, targets: List[str], plugins: List[str], results: Dict[str, bool]):
        """记录同步历史"""
        try:
            record = {
                "timestamp": datetime.now().isoformat(),
                "source_browser": source,
                "target_browsers": targets,
                "plugins": plugins,
                "results": results,
                "success_count": sum(results.values()),
                "total_targets": len(targets)
            }
            
            self.sync_history.append(record)
            
            # 保持历史记录数量限制
            max_history = get_config('plugin_sync.max_history_records', 100)
            if len(self.sync_history) > max_history:
                self.sync_history = self.sync_history[-max_history:]
            
            self._save_sync_history()
            
        except Exception as e:
            self.logger.error(f"记录同步历史失败: {e}")
    
    def get_sync_history(self, limit: int = 10) -> List[Dict]:
        """获取同步历史"""
        return self.sync_history[-limit:] if limit > 0 else self.sync_history
    
    def cleanup_backups(self, keep_days: int = 7):
        """清理过期备份"""
        try:
            current_time = time.time()
            cutoff_time = current_time - (keep_days * 24 * 3600)
            
            cleaned_count = 0
            
            for backup_dir in self.backup_dir.iterdir():
                if backup_dir.is_dir():
                    dir_time = backup_dir.stat().st_mtime
                    if dir_time < cutoff_time:
                        shutil.rmtree(backup_dir)
                        cleaned_count += 1
            
            self.logger.info(f"备份清理完成: 删除 {cleaned_count} 个过期备份")
            
        except Exception as e:
            self.logger.error(f"清理备份失败: {e}")

# 全局插件同步管理器实例
plugin_sync_manager = PluginSyncManager()

def scan_plugins(browser_name: str) -> Dict[str, Dict]:
    """快捷函数：扫描浏览器插件"""
    return plugin_sync_manager.scan_browser_plugins(browser_name)

def generate_matrix() -> Dict[str, Dict]:
    """快捷函数：生成插件矩阵"""
    return plugin_sync_manager.generate_plugin_matrix()

def sync_plugins(source: str, targets: List[str], plugins: Optional[List[str]] = None) -> Dict[str, bool]:
    """快捷函数：同步插件"""
    return plugin_sync_manager.sync_plugins(source, targets, plugins)

if __name__ == "__main__":
    # 测试插件同步管理器
    print("🔄 插件同步管理器测试")
    
    # 获取浏览器列表
    browsers = plugin_sync_manager.browser_manager.list_browsers()
    print(f"可用浏览器: {len(browsers)}个")
    
    if browsers:
        # 测试扫描插件
        test_browser = browsers[0]['name']
        plugins = scan_plugins(test_browser)
        print(f"浏览器 '{test_browser}' 插件: {len(plugins)}个")
        
        # 生成插件矩阵
        matrix = generate_matrix()
        if matrix:
            stats = matrix.get('statistics', {})
            print(f"插件矩阵: {stats}")
    
    print("✅ 插件同步管理器测试完成")
