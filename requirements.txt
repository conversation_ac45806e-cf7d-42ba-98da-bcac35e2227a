# 浏览器多账号绿色版 v2.2.1 - Python依赖文件
# 项目地址：https://github.com/your-repo/browsers
# 更新时间：2025-07-25

# ===== 核心依赖 =====
# Windows COM组件支持（快捷方式创建）
pywin32>=306

# 网络请求库（图标下载、更新检查）
requests>=2.31.0

# 图像处理库（图标格式转换）
Pillow>=10.0.0

# JSON配置文件处理（Python标准库，无需安装）
# json

# 图形界面库（Python标准库，无需安装）
# tkinter

# 文件系统操作（Python标准库，无需安装）
# os, shutil, pathlib

# 多线程支持（Python标准库，无需安装）
# threading

# 正则表达式（Python标准库，无需安装）
# re

# 时间处理（Python标准库，无需安装）
# datetime

# 系统信息（Python标准库，无需安装）
# platform, sys

# ===== 可选依赖 =====
# 更好的HTTP请求处理
urllib3>=2.0.0

# 更好的JSON处理
# 注：使用标准库json即可

# ===== 开发依赖（可选）=====
# 代码格式化
# black>=23.0.0

# 代码检查
# flake8>=6.0.0

# 类型检查
# mypy>=1.5.0

# ===== 系统要求 =====
# Python >= 3.7
# Windows 7/8/10/11
# Chrome Portable (已包含在项目中)

# ===== 安装说明 =====
# 1. 确保Python 3.7+已安装
# 2. 运行：pip install -r requirements.txt
# 3. 或者运行启动管理器，自动检查和安装依赖

# ===== 版本兼容性 =====
# Python 3.7+  ✅ 完全支持
# Python 3.8+  ✅ 推荐版本
# Python 3.9+  ✅ 最佳性能
# Python 3.10+ ✅ 最新特性
# Python 3.11+ ✅ 最新特性
# Python 3.12+ ✅ 最新特性
# Python 3.13+ ✅ 最新特性

# ===== 平台支持 =====
# Windows 7   ✅ 基础支持
# Windows 8   ✅ 完全支持  
# Windows 10  ✅ 推荐平台
# Windows 11  ✅ 最佳体验
