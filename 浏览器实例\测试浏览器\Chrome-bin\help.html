<!DOCTYPE html>
<html>
  <head>
    <title>Google Chrome Portable Help</title>
    <link rel="alternate" type="application/rss+xml" title="PortableApps.com" href="https://portableapps.com/feeds/general" />
    <link rel="icon" type="image/vnd.microsoft.icon" href="Other/Help/images/favicon.ico" />
    <link rel="SHORTCUT ICON" type="image/vnd.microsoft.icon" href="Other/Help/images/favicon.ico" />
    <style type="text/css">
      body {
        font-family: Verdana, Arial, Helvetica, sans-serif;
        font-size: 10pt;
        color: #000;
        margin: 20px;
        background: #E6E8EA;
        text-align: center;
      }
      a {
        color: #B31616;
        font-weight: bold;
      }
      a:hover {
        color: red;
      }
      h1, h2, h3, h4, h5, h6 {
        font-family: Arial, sans-serif;
        font-weight: normal;
      }
      h1 {
        color: #B31616;
        font-weight: bold;
        letter-spacing: -2px;
        font-size: 2.2em;
        border-bottom: 1px solid silver;
        padding-bottom: 5px;
      }
      h2 {
        font-size: 1.5em;
        border-bottom: 1px solid silver;
        padding-bottom: 3px;
        clear: both;
      }
      h3 {
        font-size: 1.2em;
      }
      h4 {
        font-size: 1.1em;
      }
      h5 {
        font-size: 1.0em;
      }
      h6 {
        font-size: 0.8em;
      }
      img {
        border: 0px;
      }
      ol, ul, li {
        font-size: 1.0em;
      }
      p, table, tr, td, th {
        font-size: 1.0em;
      }
      pre {
        font-family: Courier New, Courier, monospace;
        font-size: 1.0em;
        white-space: pre-wrap;
      }
      strong, b {
        font-weight: bold;
      }
      table, tr, td {
        font-size: 1.0em;
        border-collapse: collapse;
      }
      td, th {
        border: 1px solid #aaaaaa;
        border-collapse: collapse;
        padding: 3px;
      }
      th {
        background: #3667A8;
        color: white;
      }
      ol ol {
        list-style-type: lower-alpha;
      }
      .content {
        text-align: left;
        margin-left: auto;
        margin-right: auto;
        width: 780px;
        background-color: #FFFFFF;
        border-left: 1px solid Black;
        border-right: 1px solid Black;
        padding: 12px 30px;
        line-height: 150%;
      }
      .logo {
        background: #ffffff url("Other/Help/images/help_background_header.png") repeat-x;
        width: 840px;
        margin-top: 20px;
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border-right: 1px solid black;
        border-left: 1px solid black;
      }
      .footer {
        background: #ffffff url("Other/Help/images/help_background_footer.png") repeat-x;
        width: 840px;
        height: 16px;
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        border-right: 1px solid black;
        border-left: 1px solid black;
      }
      .logo img {
        padding-left: 0px;
        border: none;
        position: relative;
        top: -4px;
      }
      * html .content {
        width: 760px;
      }
      * html .logo, * html .footer {
        width: 820px;
      }
      .content h1 {
        margin: 0px;
      }
      h1.hastagline {
        border: 0px;
      }
      h2.tagline {
        color: #747673;
        clear: none;
        margin-top: 0em;
      }
      @media print { 
        body, .content {
          margin: 0px;
          padding: 0px;
        }
        .navigation, .locator, .footer a, .message, .footer-links {
          display:none;
        }            
        .footer, .content, .header {
          border: none;
        }
        a {
          text-decoration: none;
          font-weight: normal;
          color: black;
        }
      }
    </style>
  </head>
  <body>
    <div class="logo"><a href="https://portableapps.com/"><img src="Other/Help/images/help_logo_top.png" width="229" height="47" alt="PortableApps.com - Your Digital Life, Anywhere"></a></div>
    <div class="content">
      <h1 class="hastagline">Google Chrome Portable Help</h1>
      <h2 class="tagline">a new way to get online</h2>
      <p>Google Chrome Portable automatically downloads and installs Google's innovative Chrome browser and converts it into a portable form.  Google Chrome is built for speed, security, and simplicity, so it will run well even on your aunt's ancient PC.  <a href="https://portableapps.com/apps/internet/google_chrome_portable">Learn more about Google Chrome Portable...</a></p>

      <p><a href="https://portableapps.com/donate"><img src="Other/Help/images/donation_button.png" width="110" height="23" border="0" align="top" alt="Make a Donation"></a> - Support PortableApps.com's Hosting and Development</p>

      <p><a href="https://portableapps.com/apps/internet/google_chrome_portable">Go to the Google Chrome Portable Homepage &gt;&gt;</a></p>

      <p><a href="https://portableapps.com/">Get more portable apps at PortableApps.com</a></p>

      <!-- <p>This software is OSI Certified Open Source Software. OSI Certified is a certification mark of the Open Source Initiative.</p> -->

      <h2>Google Chrome Portable-Specific Issues</h2>

      <!-- <p>For support questions, see the <a href="https://portableapps.com/apps/internet/google_chrome_portable">Google Chrome Portable Homepage</a></p> -->

      <p>Installer and launcher licensed under <a href="Other/Source/License.txt">GPLv2</a>.  <a href="App/AppInfo/EULA.txt">More License Info.</a></p>

      <h3>Known Issues</h3>

      <ul>
        <li>The built-in update feature in Google Chrome will not function.</li>
        <li>Google Chrome Portable and local Chrome may not work simultaneously unless they are both the same exact version. This cannot be fixed with the current way Google Chrome's update system works.</li>
        <li>Google Chrome does not appear to run properly under some unofficial minimalistic Windows distributions.  Only official RTM builds of Windows from Microsoft are supported.</li>
        <li>Currently, Java plugins will not integrate with Java Portable, but will only be able to use a locally installed Java.  A future release of Java Portable or the PortableApps.com Platform may add the ability for Java Portable to work in Google Chrome Portable.</li>
        <li>When opting in to Portable Passwords (see GooglechromePortable.ini), a malicious individual will be able to determine the length of your passwords.</li>
      </ul>

      <h3>Using an arbitrary version of Chrome, Chromium, or Iron</h3>

      <p>There are a few ways you can set up Google Chrome Portable to use a different version of Chrome or variants.  First you must install Google Chrome Portable.  Then you can remove the existing version of Chrome from App\Chrome-bin and replace it.</p>

      <p>If your final program executable name is not App\Chrome-bin\chrome.exe you can use a GoogleChromePortable.ini file to have the launcher launch a different file.  See the file in Other\Source for more info, you can use it as a template in the same directory as the launcher.</p>

      <p>The following methods have been known to work for installing an arbitrary Chrome:</p>

      <ol>
        <li>Copy a local Chrome installation
          <p>Press Win+R to open the Run dialog.  On XP and below type:</p>

          <pre>%USERPROFILE%\Local Settings\Application Data\Google\Chrome\Application</pre>
 
          <p>On Vista and above you type:</p>

          <pre>%USERPROFILE%\AppData\Local\Google\Chrome\Application</pre>

          <p>And click OK.  This is the Google Chrome application directory.  There are two ways to copy the files:</p>

          <ol>
            <li>Open the directory corresponding with the version number you want to copy, and then the Installer directory inside that.  If you extract the Chrome.7z file you find therein into your App folder it will create a Chrome-bin directory and populate it with the proper files.
              <p>OR</p>
            </li>

            <li>Copy the entire directory you are in.  You will want to exclude the following files:
              <ul>
                <li>old_chrome.exe</li>
                <li>Any version number directory other than the newest (chrome.exe will only work with the newest).</li>
                <li>The Installer directory inside the version number directory.</li>
              </ul>

              <p>All the files and directories go into App\Chrome-bin.</p>
            </li>
          </ol>
        </li>

        <li>Download an arbitrary version installer

          <p>This url will download any Chrome version you want, provided it's still on the server (old versions seem to be removed periodically):</p>

          <pre>http://dl.google.com/chrome/install/&lt;X&gt;.&lt;Y&gt;/chrome_installer.exe</pre>

          <p>Replace &lt;X&gt; and &lt;Y&gt; with the third and fourth numbers from the version.  Example: ********** has a download url of <a href="http://dl.google.com/chrome/install/190.4/chrome_installer.exe">http://dl.google.com/chrome/install/197.11/chrome_installer.exe</a></p>

          <p>Do not run this installer as it will silently update your local Chrome!  Instead unpack it with an archiver program (7-zip works) and you will end up with a single .7z file.  Extract this file into App and it will create the Chrome-bin file and populate it with the proper files.</p>
        </li>
      </ol>
			
			<h3>Installing plugins</h3>
			
			<p>If you wish to have portable plugins, be aware that many plugins are linked to locally installed programs and will not function correctly in a portable environment.  When testing plugins be sure to test them on a system that does not have the plugin installed locally.</p>
			
			<p>Note that if a system has plugins installed locally for Firefox or Chrome, your portable Google Chrome should find and use them automatically.</p>
			
			<p>Since verison 5.0 Chrome includes a built-in Flash player so no Flash plugin is needed.</p>
			
			<p>Starting with 6.0 (currently only in the Dev channel) Chrome will have a PDF plugin capable of rendering PDF files.  No PDF plugin will be needed externally.</p>
			
			<p>To install a plugin, first you need to find the location of a locally installed plugin.  Run a local Firefox or Chrome and navigate to <a href="about:plugins">about:plugins</a>.  In Chrome, click "Details" on the right.</p>
			
			<p>You should now see each plugin's details including the file path.</p>
			
			<p>If you open the folder up and copy any relevant looking files to Google Chrome Portable\App\Chrome-bin\plugins (you'll have to create the plugins folder) it should work if the plugin supports being used this way.</p>
			
			<p>You can open <a href="about:plugins">about:plugins</a> in Google Chrome Portable to verify which plugins are being loaded and used.  Again be sure to test the plugin on a computer without that plugin installed locally!</p>

      <h3>Removing unneeded components</h3>
			
			<p>NOTE: This section is out of date.  Be sure to keep backups in case your removals break something you want.  Test thoroughly.</p>

      <p>Chrome ships with a few features that can be safely removed:</p>

      <p>To remove languages, open the App\Chrome-bin\&lt;version number&gt;\Locales directory, and remove any language file you do not need.  en-US.dll is US English and may be the only one you want.</p>

      <p>To remove DOM Inspector, remove the App\Chrome-bin\&lt;version number&gt;\Resources\Inspector directory.  DOM Inspector will still be available within Chrome as an option but will not function (the popup window will have an error message).</p>
			
			<p>To remove packaged Web Apps (GMail, Calendar, Docs) remove the appropriate directories from App\Chrome-bin\&lt;version number&gt;\Resources.  Probably not worth it since they're mostly just a few icons.</p>

      <p>In addition if you installed a custom Chrome version by hand keep in mind that the App\Chrome-bin\&lt;version number&gt;\Installer directory is not needed and should be removed.  Normal Chrome only seems to keep it around for uninstallation purposes.</p>

      <p>You can also experiment with removing App\Chrome-bin\&lt;version number&gt;\gears.dll to remove Google Gears support but I don't know if that will work or not. :)  Same with gcswf32.dll and pdf.dll for the internal flash and PDF plugins.</p>

      <p>wow_helper.exe is used for 64-bit support for the sandbox and if you don't intend to run on 64-bit you don't need it.  It is not installed on 32-bit machines by the Chrome installer.  There are also nacl64.* files which are probably 64-bit Native Client plugin stuff.</p>

      <h3>Enabling Chrome's First Run dialog</h3>

      <p>You can trigger the first run dialog in Google Chrome to import bookmarks from the local machine by deleting App\Chrome-bin\First Run and then running Google Chrome Portable.  Note that it works even if you have a profile, but I don't know if it'll preserve or wipe out your bookmarks when it imports, so be careful!</p>

      <h3>Compressing with UPX</h3>

      <p>Though PAF has allowed the installer for Google Chrome Portable to be simplified, a downside is that UPX cannot be used to automatically compress the downloaded Chrome files.  You can compress some of them by hand if you wish.</p>

      <p>Do not compress the following files:</p>

      <ul>
        <li>avcodec*.dll - Random crashes with &lt;video&gt;/&lt;audio&gt;.  UPX detects errors when you try to decompress.</li>
        <li>chrome.exe - Tabs no longer show content.</li>
        <li>chrome.dll - Not supported by UPX.</li>
				<li>wow_helper.exe, nacl64.dll, nacl64.exe - 64-bit binaries not supported by UPX.</li>
        <li>Locale DLLs - Chrome starts up with no text, pops up a blank dialog, and crashes.</li>
      </ul>

      <p>The following files <strong>CAN</strong> be compressed without breaking Chrome:</p>

      <ul>
        <li>avformat*.dll</li>
        <li>avutil*.dll</li>
        <li>chrome_launcher.exe</li>
        <li>d3dcompiler*.dll</li>
        <li>d3dx9*.dll</li>
				<li>gcswf32.dll</li>
        <li>gears.dll</li>
        <li>icudt*.dll</li>
				<li>libegl.dll</li>
				<li>libglesv2.dll</li>
				<li>npchrome_frame.dll (Compacts in Chrome 6, not in Chrome 7)</li>
				<li>pdf.dll</li>
      </ul>

      <p>New versions of Chrome may add more binaries, be sure to test Chrome thoroughly if you compress any of them.</p>

      <h3>Copying a local profile</h3>

      <p>Press Win+R to open the Run dialog.  On XP and below type:</p>

      <pre>%USERPROFILE%\Local Settings\Application Data\Google\Chrome\User Data</pre>

      <p>On Vista and above you type:</p>

      <pre>%LOCALAPPDATA%\Google\Chrome\User Data</pre>

      <p>This is your profile dir and should be copied to Data\profile.  If you did it right you will end up with a Default directory and a Local State file in Data\profile.  You may also have a few more files and folders depending on your profile settings.</p>

      <p>You should exempt Default\Cache and Default\Media Cache from the copy.  You may also want to trim your profile down a bit regularly, in particular the History Index files can get large.  You should use the in-Chrome options for purging old data, or if you know how, vaccuum the SQLLite databases in your profile.  Note that removing the Thumbnails file may seem like a good idea but then your icons on your bookmarks will be gone.</p>

      <p>You can further reduce the profile size by disabling malware and phishing protection in Chrome and then removing the Safe Browsing files from the profile.</p>

      <p>In the end it may be prudent to simply export your bookmarks from Chrome and then re-import them in a fresh profile to keep file sizes down.</p>

      <h3>Cleaning up in case of a launcher crash</h3>

      <p>If the GoogleChromePortable process does not exit normally, it will leave some bits and pieces that need to be cleaned up, as most portable launchers will:</p>

      <ol>
        <li>HKEY_CURRENT_USER\Software\Google\Update\Clients\{8A69D345-D564-463c-AFF1-A69D9E530F96}\pv will be set to the Portable Chrome version.  If the local Chrome is a different version this value will need to be adjusted to match.  If there is no local Chrome the entire key should be deleted.</li>
        <li>HKEY_CURRENT_USER\Software\Google\Update\Clients\{4ea16ac7-fd5a-47c3-875b-dbf4a2008c20}\pv will be set to the Portable Chrome version.  If the local Chrome CANARY is a different version this value will need to be adjusted to match.  If there is no local Chrome CANARY the entire key should be deleted.</li>
        <li>HKEY_CURRENT_USER\Software\Google\Update\ClientState\{8A69D345-D564-463c-AFF1-A69D9E530F96} should be deleted if Chrome is not installed locally.</li>
        <li>HKEY_CURRENT_USER\Software\Google\Update\ClientState\{4ea16ac7-fd5a-47c3-875b-dbf4a2008c20} should be deleted if Chrome CANARY is not installed locally.</li>
        <li>HKEY_CURRENT_USER\Software\Google\Rlz should be deleted.</li>
        <li>If there is no local Chrome HKEY_CLASSES_ROOT\Applications\chrome.exe, HKEY_CLASSES_ROOT\.htm\OpenWithList\chrome.exe, and HKEY_CLASSES_ROOT\.html\OpenWithList\chrome.exe should all be deleted.  Otherwise HKEY_CLASSES_ROOT\Applications\chrome.exe\shell\open\command should be set to the location of the local chrome using the following templates.
          <p>For XP:</p>

          <pre>"C:\Documents and Settings\&lt;Username&gt;\Local Settings\Application Data\Google\Chrome\Application\chrome.exe" -- "%1"</pre>

          <p>Vista and 7:</p>

          <pre>"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe" -- "%1"</pre>

          <p>Chrome overwrites this value with it's own location when run portably so the launcher will restore it on close.</p>
        </li>

        <li>Make sure there is no Data\settings\GoogleChromePortableShuttingDown and delete it if it exists.</li>
        <li>%TEMP%\GoogleChromePortable\profile should be copied back to Data\profile to avoid losing any profile data changed last session, if copying the profile locally was on.  %TEMP%\GoogleChromePortable\profile\Default\Cache should be excluded if it exists.</li>
        <li>%TEMP%\GoogleChromePortable\rlz.reg should be merged into the registry if it exists.</li>
        <li>%TEMP%\GoogleChromePortable should be deleted.</li>
	<!-- <li>Only if Java Portable is installed on your portable device:
	  <ol>
	    <li>HKEY_LOCAL_MACHINE\SOFTWARE\JavaSoft\Java Runtime Environment\BrowserJavaVersion needs to be set back to the local Java version, or deleted if there is none.  The keys under this key contain local Java versions installed (check to be sure they aren't the Java Portable version first).</li>
	    <li>HKEY_LOCAL_MACHINE\SOFTWARE\JavaSoft\Java Runtime Environment and HKEY_LOCAL_MACHINE\SOFTWARE\JavaSoft\Java Plug-in both have a key corresponding to the Portablized value name above.  The JavaHome value in both those keys is set to the Java Portable location.  If this version of Java is installed locally, the paths need to be reverted to the local path.  Otherwise the key should be deleted.</li>
	  </ol>
	</li> -->
      </ol>

      <h3>Command line options</h3>

      <p>Google Chrome has some experimental features which require command line parameters to activate.  Some others are for diagnostic purposes.  You can enable these by copying GoogleChromePortable\Other\Source\GoogleChromePortable.ini to GoogleChromePortable\ and adding the parameters to the AdditionalParameters field.  For example:</p>

      <pre>[GoogleChromePortable]
AdditionalParameters=--disable-extensions --enable-webgl</pre>

      <p>This disables extension support in Chrome and enables experimental WebGL support.</p>

      </div>
    <div class="footer"></div>
  </body>
</html>